# Project Astrum 🌌

**Reimagining the Decentralized Space – A Foundational Protocol for Next-Generation Applications & User-Centric Internets**

## Vision

Project Astrum aims to architect a new foundational network protocol and development framework, creating a decentralized layer over the existing internet. We envision a future where users have genuine sovereignty over their data and interactions, and where applications are built on principles of true decentralization, resilience, and privacy.

Our core inspiration is celestial: individual "Beacons" (nodes) forming "Constellations" (localized, trusted networks), contributing to a vast, interconnected "Universe" of applications and users.

## Core Goal

To reimagine the decentralized space by architecting a new foundational network protocol and development framework. This framework is designed to:

1.  **Enable True Decentralization:** Move beyond existing limitations to foster systems with no single points of control or failure. Each Beacon operates autonomously yet can connect to form robust Constellations.
2.  **Support Next-Generation Applications:** Provide the infrastructure for a diverse ecosystem of dApps that are scalable, efficient, and inherently user-centric.
3.  **Champion User Sovereignty:** Ensure users have genuine ownership and control over their data, identity (via Astrum DIDs, built upon `libp2p PeerId`s), and digital assets.
4.  **Facilitate Privacy & Anonymity:** Build in capabilities or make it easier to create solutions that prioritize user privacy and allow for anonymous interactions where appropriate. Communication within a Constellation is primarily confined to its participating Beacons.
5.  **Grow in Power & Utility (The Constellation Effect):** Design the system so that its resilience, value, and the richness of its ecosystem increase as more Beacons (participants) and applications join and interact.

## Project Architecture (Conceptual)

Project Astrum is envisioned with distinct but interconnected layers:

1.  **Network Layer (The "Beacon" & "Constellation" Layer):**
    *   **Focus:** Establishing and maintaining secure, resilient P2P connections between Astrum Beacons.
    *   **Beacon:** An OS-level network manager; the fundamental node in the Astrum network. Each Beacon is responsible for its own connections and data interactions.
    *   **Constellation Topology:** Beacons connect to form trusted, potentially small and private, groups. Data sharing is primarily scoped within these directly connected peers or defined "Constellations," rather than broadcast globally by default.
    *   **Technology:** Built using Rust and `libp2p` for robust, cross-platform P2P networking, incorporating NAT traversal (Relay, DCUtR/hole-punching) and decentralized identity (`PeerId` as a base for Astrum DIDs).
    *   **Current Milestone:** Establishing reliable P2P connections between Beacon CLI instances across different networks (Linux initially, then Windows/Android). Implementing a "Phonebook" for managing peer state.

2.  **Data Layer (Future Work):**
    *   **Focus:** Defining protocols and mechanisms for structured data exchange, storage, and synchronization *over* the established Network Layer connections.
    *   **Vision:** Enabling applications to securely and efficiently share, store, and manage data in a decentralized manner, respecting user sovereignty and privacy. This layer will leverage the "relation-based" connections formed at the Network Layer.

## Current Status & Focus

We are in the **early stages of development**, focusing on the **Network Layer**.

Our immediate goals are:
*   Implementing the core **Astrum Beacon** CLI application in Rust.
*   Establishing robust P2P connectivity between Beacons on different networks using `libp2p`, including:
    *   Secure channels (Noise protocol).
    *   Stream multiplexing (Yamux).
    *   NAT traversal (Relay client, DCUtR for hole punching).
    *   Peer discovery (Identify, potentially mDNS for local).
*   Developing a "Phonebook" within each Beacon to manage peer information, connection status, and known addresses.
*   Utilizing `libp2p PeerId` as the foundational Decentralized Identifier (DID) for Beacons, with plans to evolve this into a richer `did:astrum` specification.

## Why Rust & `libp2p`?

*   **Rust:** For its performance, memory safety, and fearless concurrency, crucial for building a foundational and reliable network protocol.
*   **`libp2p`:** A modular P2P networking stack that provides the building blocks for peer identity, transports, secure communication, and peer discovery, allowing us to focus on Astrum's unique logic.

## How to Contribute

We welcome contributors who are passionate about decentralization, privacy, and building the next generation of internet infrastructure!

1.  **Understand the Vision:** Read through this README and any linked design documents (to be added).
2.  **Set Up Your Environment:**
    *   Install Rust: [https://rustup.rs/](https://rustup.rs/)
    *   Clone the repository: `git clone [URL_OF_YOUR_REPO]`
    *   Explore the current codebase (likely in `src/main.rs` for the Beacon node).
3.  **Check Out the Issues:** Look for issues tagged `good first issue` or `help wanted`. (You'll need to create these as you identify tasks!)
4.  **Current Tasks / Areas for Contribution (Examples - you'll need to create specific issues):**
    *   **Beacon CLI Enhancements:** Improving command-line arguments, output formatting, user experience.
    *   **Phonebook Features:** Implementing persistence for the phonebook, adding more detailed peer status tracking.
    *   **Testing:** Writing unit and integration tests for P2P connectivity scenarios.
    *   **Cross-Platform Builds:** Assisting with build configurations and testing for Windows and Android (native library via JNI).
    *   **Custom Protocol Development:** Designing and implementing simple message-passing protocols on top of `libp2p` for Beacon-to-Beacon communication (once core connectivity is stable).
    *   **Documentation:** Improving this README, adding architectural diagrams, documenting code.
    *   **Research:** Exploring advanced DID concepts, DHT usage for DID document resolution, or specific data layer protocols.


## Getting Started with the Code (Example)

```bash
# Install Rust if you haven't already
# curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
# source $HOME/.cargo/env

# Clone the repository
# git clone [YOUR_REPO_URL]
# cd project-astrum

# Build the project
cargo build

# Run the Beacon node (example, arguments will evolve)
# ./target/debug/astrum_beacon --listen-via-relay --relay-server-addr /ip4/... --relay-server-id ...
# ./target/debug/astrum_beacon --dial /ip4/.../p2p/.../p2p-circuit/p2p/...
