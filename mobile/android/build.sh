#!/bin/bash
# Build script for Android cross-compilation

set -e

echo "🤖 Building Astrum for Android..."

# Check if Android NDK is installed
if [ -z "$ANDROID_NDK_HOME" ]; then
    echo "❌ ANDROID_NDK_HOME not set. Please install Android NDK."
    echo "Download from: https://developer.android.com/ndk/downloads"
    exit 1
fi

# Install Android targets
echo "📦 Installing Android targets..."
rustup target add aarch64-linux-android
rustup target add armv7-linux-androideabi
rustup target add x86_64-linux-android
rustup target add i686-linux-android

# Set up cargo config for Android
mkdir -p .cargo
cat > .cargo/config.toml << EOF
[target.aarch64-linux-android]
linker = "$ANDROID_NDK_HOME/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android21-clang"

[target.armv7-linux-androideabi]
linker = "$ANDROID_NDK_HOME/toolchains/llvm/prebuilt/linux-x86_64/bin/armv7a-linux-androideabi21-clang"

[target.x86_64-linux-android]
linker = "$ANDROID_NDK_HOME/toolchains/llvm/prebuilt/linux-x86_64/bin/x86_64-linux-android21-clang"

[target.i686-linux-android]
linker = "$ANDROID_NDK_HOME/toolchains/llvm/prebuilt/linux-x86_64/bin/i686-linux-android21-clang"
EOF

# Build for different Android architectures
echo "🔨 Building for ARM64 (most modern Android devices)..."
cargo build --release --target aarch64-linux-android

echo "🔨 Building for ARMv7 (older Android devices)..."
cargo build --release --target armv7-linux-androideabi

echo "🔨 Building for x86_64 (Android emulator)..."
cargo build --release --target x86_64-linux-android

# Create mobile directory structure
mkdir -p mobile/android/libs/{arm64-v8a,armeabi-v7a,x86_64}

# Copy binaries
cp target/aarch64-linux-android/release/astrum_beacon mobile/android/libs/arm64-v8a/astrum
cp target/armv7-linux-androideabi/release/astrum_beacon mobile/android/libs/armeabi-v7a/astrum
cp target/x86_64-linux-android/release/astrum_beacon mobile/android/libs/x86_64/astrum

echo "✅ Android builds complete!"
echo "📱 Binaries available in mobile/android/libs/"
echo ""
echo "Next steps:"
echo "1. Copy to Android device: adb push mobile/android/libs/arm64-v8a/astrum /data/local/tmp/"
echo "2. Make executable: adb shell chmod +x /data/local/tmp/astrum"
echo "3. Run: adb shell /data/local/tmp/astrum --help"
