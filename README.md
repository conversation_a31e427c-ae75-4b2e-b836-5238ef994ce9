# Project Astrum 🌌

**Reimagining the Decentralized Space – A Foundational Protocol for Next-Generation Applications & User-Centric Internets**

## Vision

Project Astrum aims to architect a new foundational network protocol and development framework, creating a decentralized layer over the existing internet. We envision a future where users have genuine sovereignty over their data and interactions, and where applications are built on principles of true decentralization, resilience, and privacy.

Our core inspiration is celestial: individual "Beacons" (nodes) forming "Constellations" (localized, trusted networks), contributing to a vast, interconnected "Universe" of applications and users.

## Core Goal

To reimagine the decentralized space by architecting a new foundational network protocol and development framework. This framework is designed to:

1.  **Enable True Decentralization:** Move beyond existing limitations to foster systems with no single points of control or failure. Each Beacon operates autonomously yet can connect to form robust Constellations.
2.  **Support Next-Generation Applications:** Provide the infrastructure for a diverse ecosystem of dApps that are scalable, efficient, and inherently user-centric.
3.  **Champion User Sovereignty:** Ensure users have genuine ownership and control over their data, identity (via Astrum DIDs, built upon `libp2p PeerId`s), and digital assets.
4.  **Facilitate Privacy & Anonymity:** Build in capabilities or make it easier to create solutions that prioritize user privacy and allow for anonymous interactions where appropriate. Communication within a Constellation is primarily confined to its participating Beacons.
5.  **Grow in Power & Utility (The Constellation Effect):** Design the system so that its resilience, value, and the richness of its ecosystem increase as more Beacons (participants) and applications join and interact.

## Project Architecture (Conceptual)

Project Astrum is envisioned with distinct but interconnected layers:

1.  **Network Layer (The "Beacon" & "Constellation" Layer):**
    *   **Focus:** Establishing and maintaining secure, resilient P2P connections between Astrum Beacons.
    *   **Beacon:** An OS-level network manager; the fundamental node in the Astrum network. Each Beacon is responsible for its own connections and data interactions.
    *   **Constellation Topology:** Beacons connect to form trusted, potentially small and private, groups. Data sharing is primarily scoped within these directly connected peers or defined "Constellations," rather than broadcast globally by default.
    *   **Technology:** Built using Rust and `libp2p` for robust, cross-platform P2P networking, incorporating NAT traversal (Relay, DCUtR/hole-punching) and decentralized identity (`PeerId` as a base for Astrum DIDs).
    *   **Current Milestone:** Establishing reliable P2P connections between Beacon CLI instances across different networks (Linux initially, then Windows/Android). Implementing a "Phonebook" for managing peer state.

2.  **Data Layer (Future Work):**
    *   **Focus:** Defining protocols and mechanisms for structured data exchange, storage, and synchronization *over* the established Network Layer connections.
    *   **Vision:** Enabling applications to securely and efficiently share, store, and manage data in a decentralized manner, respecting user sovereignty and privacy. This layer will leverage the "relation-based" connections formed at the Network Layer.

## Current Status & Focus

We are in the **early stages of development**, focusing on the **Network Layer**.

Our immediate goals are:
*   Implementing the core **Astrum Beacon** CLI application in Rust.
*   Establishing robust P2P connectivity between Beacons on different networks using `libp2p`, including:
    *   Secure channels (Noise protocol).
    *   Stream multiplexing (Yamux).
    *   NAT traversal (Relay client, DCUtR for hole punching).
    *   Peer discovery (Identify, potentially mDNS for local).
*   Developing a "Phonebook" within each Beacon to manage peer information, connection status, and known addresses.
*   Utilizing `libp2p PeerId` as the foundational Decentralized Identifier (DID) for Beacons, with plans to evolve this into a richer `did:astrum` specification.

## Why Rust & `libp2p`?

*   **Rust:** For its performance, memory safety, and fearless concurrency, crucial for building a foundational and reliable network protocol.
*   **`libp2p`:** A modular P2P networking stack that provides the building blocks for peer identity, transports, secure communication, and peer discovery, allowing us to focus on Astrum's unique logic.

## How to Contribute

We welcome contributors who are passionate about decentralization, privacy, and building the next generation of internet infrastructure!

1.  **Understand the Vision:** Read through this README and any linked design documents (to be added).
2.  **Set Up Your Environment:**
    *   Install Rust: [https://rustup.rs/](https://rustup.rs/)
    *   Clone the repository: `git clone [URL_OF_YOUR_REPO]`
    *   Explore the current codebase (likely in `src/main.rs` for the Beacon node).
3.  **Check Out the Issues:** Look for issues tagged `good first issue` or `help wanted`. (You'll need to create these as you identify tasks!)
4.  **Current Tasks / Areas for Contribution (Examples - you'll need to create specific issues):**
    *   **Beacon CLI Enhancements:** Improving command-line arguments, output formatting, user experience.
    *   **Phonebook Features:** Implementing persistence for the phonebook, adding more detailed peer status tracking.
    *   **Testing:** Writing unit and integration tests for P2P connectivity scenarios.
    *   **Cross-Platform Builds:** Assisting with build configurations and testing for Windows and Android (native library via JNI).
    *   **Custom Protocol Development:** Designing and implementing simple message-passing protocols on top of `libp2p` for Beacon-to-Beacon communication (once core connectivity is stable).
    *   **Documentation:** Improving this README, adding architectural diagrams, documenting code.
    *   **Research:** Exploring advanced DID concepts, DHT usage for DID document resolution, or specific data layer protocols.


## Getting Started with the Code (Example)

```bash
# Install Rust if you haven't already
# curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
# source $HOME/.cargo/env

# Clone the repository
# git clone [YOUR_REPO_URL]
# cd project-astrum

# Build the project
cargo build

# Run the Beacon node
cargo run -- start

# Show help for all available commands
cargo run -- --help
```

## Comprehensive Usage Guide

### Prerequisites

- Rust (latest stable version)
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/Project-Astrum.git
cd Project-Astrum
```

2. Build the project:
```bash
cargo build --release
```

### Command Line Interface

The Astrum Beacon provides a comprehensive CLI for managing your node:

#### Basic Commands

```bash
# Show help
cargo run -- --help

# Show beacon status
cargo run -- status

# Show local DID document
cargo run -- show-did

# Generate default configuration
cargo run -- generate-config --output my-config.toml
```

#### Network Operations

```bash
# Start the beacon with default settings
cargo run -- start

# Start with custom settings
cargo run -- start --listen "/ip4/0.0.0.0/tcp/4001" --enable-relay --disable-mdns

# Connect to a specific peer
cargo run -- connect "/ip4/*************/tcp/4001/p2p/12D3KooW..."
```

#### Constellation Management

```bash
# Create a new constellation
cargo run -- create-constellation --name "My Constellation"

# Create a private constellation
cargo run -- create-constellation --name "Private Group" --private

# Join an existing constellation
cargo run -- join-constellation constellation_1234567890
```

#### DID Operations

```bash
# Show your local DID document
cargo run -- show-did

# Publish your DID document to the network
cargo run -- publish-did

# Resolve another peer's DID
cargo run -- resolve-did "did:astrum:12D3KooW..."
```

### Configuration

Astrum uses a TOML configuration file for advanced settings. Generate a default configuration:

```bash
cargo run -- generate-config --output astrum.toml
```

Example configuration:

```toml
[beacon]
name = "My Astrum Beacon"
data_dir = "./astrum_data"
log_level = "info"

[network]
listen_addresses = ["/ip4/0.0.0.0/tcp/0"]
enable_mdns = true
enable_relay = true
relay_servers = []
bootstrap_peers = []
max_connections = 50

[phonebook]
persist_to_disk = true
file_path = "./astrum_data/phonebook.json"
max_entries = 1000

[constellation]
auto_join = false
max_constellation_size = 10
trust_threshold = 0.7
```

### Architecture Overview

The current implementation includes:

#### ✅ **Implemented Features**

- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **CLI Interface**: Comprehensive command-line interface for all operations
- **P2P Networking**: Full libp2p integration with Gossipsub, Kademlia DHT, mDNS, Identify, Relay, and DCUtR
- **DID System**: Complete Astrum DID implementation with W3C-compliant DID documents
- **Constellation Management**: Create, join, and manage trusted peer groups
- **Phonebook**: Persistent peer management with trust scoring
- **Configuration System**: Flexible TOML-based configuration with validation
- **Error Handling**: Comprehensive error handling throughout the system
- **Testing**: Unit tests for core functionality

#### 🔄 **Current Capabilities**

- **Beacon Nodes**: Fully functional P2P nodes with unique DIDs
- **Peer Discovery**: Automatic peer discovery via mDNS and DHT
- **NAT Traversal**: Relay support and hole punching for connectivity
- **Message Broadcasting**: Gossipsub-based message distribution
- **Trust Management**: Peer trust scoring and relationship tracking
- **Data Persistence**: Phonebook and configuration persistence

#### 🚧 **Future Development**

- **Data Layer**: Structured data exchange and synchronization protocols
- **Advanced DID Resolution**: Network-based DID document resolution
- **Constellation Protocols**: Enhanced constellation-specific communication
- **Application Framework**: APIs for building Astrum-based applications
- **Cross-Platform Support**: Mobile and embedded device support

### Testing

Run the test suite:

```bash
# Run unit tests
cargo test --lib

# Run all tests (may have some integration test issues due to networking)
cargo test
```

### Development

The project is structured as follows:

- `src/main.rs` - CLI interface and application entry point
- `src/beacon.rs` - Main Beacon implementation
- `src/protocols.rs` - libp2p protocol implementations
- `src/constellation.rs` - Constellation management
- `src/phonebook.rs` - Peer management and storage
- `src/did.rs` - Astrum DID system
- `src/config.rs` - Configuration management
- `src/error.rs` - Error types and handling

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### License

This project is licensed under the MIT License - see the LICENSE file for details.
