use libp2p::{
    gossipsub, identity,
    kad::{self, <PERSON><PERSON><PERSON><PERSON>, KademliaConfig, store::MemoryStore},
    mdns, ping, identify, relay, dcutr,
    swarm::NetworkBehaviour,
    PeerId,
};
use std::time::Duration;

/// Combined network behaviour for Astrum Beacon
#[derive(NetworkBehaviour)]
#[behaviour(out_event = "AstrumBehaviourEvent")]
pub struct AstrumBehaviour {
    pub gossipsub: gossipsub::Behaviour,
    pub kademlia: Kademlia<MemoryStore>,
    pub mdns: mdns::tokio::Behaviour,
    pub ping: ping::Behaviour,
    pub identify: identify::Behaviour,
    // Temporarily disabled due to libp2p relay client issues
    // pub relay_client: relay::client::Behaviour,
    // pub dcutr: dcutr::Behaviour,
}

#[derive(Debug)]
pub enum AstrumBehaviourEvent {
    Gossipsub(gossipsub::Event),
    Kade<PERSON><PERSON>(kad::KademliaEvent),
    Mdns(mdns::Event),
    Ping(ping::Event),
    Identify(identify::Event),
    // Temporarily disabled
    // RelayClient(relay::client::Event),
    // Dcutr(dcutr::Event),
}

impl From<gossipsub::Event> for AstrumBehaviourEvent {
    fn from(event: gossipsub::Event) -> Self {
        AstrumBehaviourEvent::Gossipsub(event)
    }
}

impl From<kad::KademliaEvent> for AstrumBehaviourEvent {
    fn from(event: kad::KademliaEvent) -> Self {
        AstrumBehaviourEvent::Kademlia(event)
    }
}

impl From<mdns::Event> for AstrumBehaviourEvent {
    fn from(event: mdns::Event) -> Self {
        AstrumBehaviourEvent::Mdns(event)
    }
}

impl From<ping::Event> for AstrumBehaviourEvent {
    fn from(event: ping::Event) -> Self {
        AstrumBehaviourEvent::Ping(event)
    }
}

impl From<identify::Event> for AstrumBehaviourEvent {
    fn from(event: identify::Event) -> Self {
        AstrumBehaviourEvent::Identify(event)
    }
}

// Temporarily disabled
// impl From<relay::client::Event> for AstrumBehaviourEvent {
//     fn from(event: relay::client::Event) -> Self {
//         AstrumBehaviourEvent::RelayClient(event)
//     }
// }

// impl From<dcutr::Event> for AstrumBehaviourEvent {
//     fn from(event: dcutr::Event) -> Self {
//         AstrumBehaviourEvent::Dcutr(event)
//     }
// }

impl AstrumBehaviour {
    pub fn new(
        local_peer_id: PeerId,
        local_key: &identity::Keypair,
        enable_mdns: bool,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Configure Gossipsub
        let gossipsub_config = gossipsub::ConfigBuilder::default()
            .heartbeat_interval(Duration::from_secs(10))
            .validation_mode(gossipsub::ValidationMode::Strict)
            .build()?;

        let gossipsub = gossipsub::Behaviour::new(
            gossipsub::MessageAuthenticity::Signed(local_key.clone()),
            gossipsub_config,
        )?;

        // Configure Kademlia DHT
        let mut kad_config = KademliaConfig::default();
        kad_config.set_query_timeout(Duration::from_secs(5 * 60));
        let store = MemoryStore::new(local_peer_id);
        let kademlia = Kademlia::with_config(local_peer_id, store, kad_config);

        // Configure mDNS (if enabled)
        let mdns = if enable_mdns {
            mdns::tokio::Behaviour::new(mdns::Config::default(), local_peer_id)?
        } else {
            mdns::tokio::Behaviour::new(
                mdns::Config {
                    enable_ipv6: false,
                    ..Default::default()
                },
                local_peer_id,
            )?
        };

        // Configure Ping
        let ping = ping::Behaviour::new(ping::Config::new());

        // Configure Identify
        let identify = identify::Behaviour::new(identify::Config::new(
            "/astrum/1.0.0".to_string(),
            local_key.public(),
        ));

        // Temporarily disabled due to libp2p relay client issues
        // let (_relay_transport, relay_client) = relay::client::new(local_peer_id);
        // let dcutr = dcutr::Behaviour::new(local_peer_id);

        Ok(AstrumBehaviour {
            gossipsub,
            kademlia,
            mdns,
            ping,
            identify,
            // relay_client,
            // dcutr,
        })
    }

    /// Subscribe to a gossipsub topic
    pub fn subscribe_to_topic(&mut self, topic: &gossipsub::IdentTopic) -> Result<bool, gossipsub::SubscriptionError> {
        self.gossipsub.subscribe(topic)
    }

    /// Publish a message to a gossipsub topic
    pub fn publish_message(&mut self, topic: gossipsub::IdentTopic, data: Vec<u8>) -> Result<gossipsub::MessageId, gossipsub::PublishError> {
        self.gossipsub.publish(topic, data)
    }

    /// Add a peer to the Kademlia routing table
    pub fn add_peer_to_dht(&mut self, peer_id: PeerId, address: libp2p::Multiaddr) {
        self.kademlia.add_address(&peer_id, address);
    }

    /// Bootstrap the Kademlia DHT
    pub fn bootstrap_dht(&mut self) -> Result<kad::QueryId, kad::NoKnownPeers> {
        self.kademlia.bootstrap()
    }

    /// Get the closest peers to a key from Kademlia
    pub fn get_closest_peers(&mut self, key: Vec<u8>) -> kad::QueryId {
        self.kademlia.get_closest_peers(key)
    }

    /// Put a record in the Kademlia DHT
    pub fn put_record(&mut self, record: kad::Record) -> Result<kad::QueryId, kad::store::Error> {
        self.kademlia.put_record(record, kad::Quorum::One)
    }

    /// Get a record from the Kademlia DHT
    pub fn get_record(&mut self, key: kad::RecordKey) -> kad::QueryId {
        self.kademlia.get_record(key)
    }
}

/// Protocol constants and utilities
pub mod constants {
    pub const ASTRUM_PROTOCOL_VERSION: &str = "/astrum/1.0.0";
    pub const CONSTELLATION_TOPIC_PREFIX: &str = "astrum-constellation";
    pub const BEACON_DISCOVERY_TOPIC: &str = "astrum-beacon-discovery";
    pub const DID_RESOLUTION_TOPIC: &str = "astrum-did-resolution";
}

/// Message types for Astrum protocols
pub mod messages {
    use serde::{Deserialize, Serialize};
    use libp2p::PeerId;
    use crate::did::AstrumDid;

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub enum AstrumMessage {
        BeaconAnnouncement(BeaconAnnouncement),
        ConstellationInvite(ConstellationInvite),
        ConstellationJoin(ConstellationJoin),
        ConstellationLeave(ConstellationLeave),
        DidResolution(DidResolution),
        TrustUpdate(TrustUpdate),
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct BeaconAnnouncement {
        pub did: AstrumDid,
        pub addresses: Vec<String>, // Multiaddr as strings
        pub capabilities: Vec<String>,
        pub timestamp: u64,
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ConstellationInvite {
        pub constellation_id: String,
        pub inviter_did: AstrumDid,
        pub invited_peer: PeerId,
        pub message: Option<String>,
        pub timestamp: u64,
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ConstellationJoin {
        pub constellation_id: String,
        pub joiner_did: AstrumDid,
        pub timestamp: u64,
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ConstellationLeave {
        pub constellation_id: String,
        pub leaver_did: AstrumDid,
        pub timestamp: u64,
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct DidResolution {
        pub did: AstrumDid,
        pub addresses: Vec<String>,
        pub public_key: Vec<u8>,
        pub timestamp: u64,
    }

    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct TrustUpdate {
        pub from_did: AstrumDid,
        pub to_did: AstrumDid,
        pub trust_score: f64,
        pub constellation_id: Option<String>,
        pub timestamp: u64,
    }
}
