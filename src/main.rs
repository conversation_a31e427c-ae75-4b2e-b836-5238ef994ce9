use clap::{Parser, Subcommand};
use std::path::PathBuf;
use tracing::{info, error};
use tracing_subscriber;

use astrum::{Beacon, Config, Result, AstrumError, AstrumDaemon, AstrumClient};

#[derive(Parser)]
#[command(name = "astrum_beacon")]
#[command(about = "Astrum Beacon - A decentralized network node")]
#[command(version = "0.1.0")]
struct Cli {
    /// Configuration file path
    #[arg(short, long)]
    config: Option<PathBuf>,

    /// Log level (trace, debug, info, warn, error)
    #[arg(short, long, default_value = "info")]
    log_level: String,

    /// Data directory for storing beacon data
    #[arg(short, long)]
    data_dir: Option<PathBuf>,

    #[command(subcommand)]
    command: Option<Commands>,
}

#[derive(Subcommand)]
enum Commands {
    /// Start the beacon node
    Start {
        /// Listen addresses (can be specified multiple times)
        #[arg(short, long)]
        listen: Vec<String>,

        /// Bootstrap peer addresses
        #[arg(short, long)]
        bootstrap: Vec<String>,

        /// Enable relay mode
        #[arg(long)]
        enable_relay: bool,

        /// Disable mDNS discovery
        #[arg(long)]
        disable_mdns: bool,
    },
    /// Connect to a specific peer
    Connect {
        /// Peer address to connect to
        address: String,
    },
    /// Create a new constellation
    CreateConstellation {
        /// Constellation name
        #[arg(short, long)]
        name: Option<String>,

        /// Make constellation private
        #[arg(long)]
        private: bool,
    },
    /// Join an existing constellation
    JoinConstellation {
        /// Constellation ID to join
        constellation_id: String,
    },
    /// Show beacon status
    Status,
    /// Generate a default configuration file
    GenerateConfig {
        /// Output path for the configuration file
        #[arg(short, long, default_value = "astrum.toml")]
        output: PathBuf,
    },
    /// Publish DID document to the network
    PublishDid,
    /// Resolve a DID
    ResolveDid {
        /// DID to resolve
        did: String,
    },
    /// Show local DID document
    ShowDid,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize tracing
    let log_level = match cli.log_level.as_str() {
        "trace" => tracing::Level::TRACE,
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::fmt()
        .with_max_level(log_level)
        .init();

    // Load or create configuration
    let mut config = Config::load_or_default(cli.config.as_ref())
        .map_err(|e| {
            eprintln!("Failed to load configuration: {}", e);
            e
        })?;

    // Override config with CLI arguments
    if let Some(data_dir) = cli.data_dir {
        config.beacon.data_dir = data_dir;
    }

    match cli.command {
        Some(Commands::Start { listen, bootstrap, enable_relay, disable_mdns }) => {
            // Override network configuration with CLI arguments
            config.merge_with_cli_args(
                if listen.is_empty() { None } else { Some(listen) },
                if bootstrap.is_empty() { None } else { Some(bootstrap) },
                Some(enable_relay),
                Some(!disable_mdns),
                None,
            ).map_err(|e| {
                eprintln!("Configuration error: {}", e);
                e
            })?;

            start_beacon(config).await
        }
        Some(Commands::Connect { address }) => {
            connect_to_peer(config, address).await
        }
        Some(Commands::CreateConstellation { name, private }) => {
            create_constellation(config, name, private).await
        }
        Some(Commands::JoinConstellation { constellation_id }) => {
            join_constellation(config, constellation_id).await
        }
        Some(Commands::Status) => {
            show_status(config).await
        }
        Some(Commands::GenerateConfig { output }) => {
            generate_config(output).await
        }
        Some(Commands::PublishDid) => {
            publish_did(config).await
        }
        Some(Commands::ResolveDid { did }) => {
            resolve_did(config, did).await
        }
        Some(Commands::ShowDid) => {
            show_did(config).await
        }
        None => {
            // Default action: start the beacon
            start_beacon(config).await
        }
    }
}

async fn start_beacon(config: Config) -> Result<()> {
    info!("Starting Astrum Beacon...");

    // Validate configuration before starting
    config.validate().map_err(|e| {
        eprintln!("Configuration validation failed: {}", e);
        e
    })?;

    let mut beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;

    info!("Beacon initialized with DID: {}", beacon.local_did());
    info!("Local Peer ID: {}", beacon.local_peer_id());
    info!("Data directory: {:?}", beacon.config.beacon.data_dir);

    // Set up graceful shutdown
    let mut sigterm = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
        .map_err(|e| astrum::AstrumError::IoError(e))?;
    let mut sigint = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::interrupt())
        .map_err(|e| astrum::AstrumError::IoError(e))?;

    tokio::select! {
        result = beacon.run() => {
            match result {
                Ok(()) => info!("Beacon stopped gracefully"),
                Err(e) => {
                    eprintln!("Beacon error: {}", e);
                    return Err(e);
                }
            }
        }
        _ = sigterm.recv() => {
            info!("Received SIGTERM, shutting down gracefully...");
        }
        _ = sigint.recv() => {
            info!("Received SIGINT, shutting down gracefully...");
        }
    }

    Ok(())
}

async fn connect_to_peer(config: Config, address: String) -> Result<()> {
    info!("Connecting to peer: {}", address);

    config.validate().map_err(|e| {
        eprintln!("Configuration validation failed: {}", e);
        e
    })?;

    let mut beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;

    let addr = address.parse()
        .map_err(|e| {
            eprintln!("Invalid address '{}': {}", address, e);
            astrum::AstrumError::NetworkError(format!("Invalid address: {}", e))
        })?;

    beacon.dial(addr).map_err(|e| {
        eprintln!("Failed to dial peer: {}", e);
        e
    })?;

    info!("Attempting to connect to {}", address);

    // Run for a short time to establish connection
    match tokio::time::timeout(std::time::Duration::from_secs(30), beacon.run()).await {
        Ok(result) => result,
        Err(_) => {
            info!("Connection attempt timed out");
            Ok(())
        }
    }
}

async fn create_constellation(config: Config, name: Option<String>, private: bool) -> Result<()> {
    info!("Creating constellation...");

    config.validate().map_err(|e| {
        eprintln!("Configuration validation failed: {}", e);
        e
    })?;

    let mut beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;

    let constellation_id = beacon.create_constellation(name.clone(), private).map_err(|e| {
        eprintln!("Failed to create constellation: {}", e);
        e
    })?;

    info!("Created constellation '{}' with ID: {}",
          name.unwrap_or_else(|| "Unnamed".to_string()),
          constellation_id);

    // Publish the constellation creation if not private
    if !private {
        if let Err(e) = beacon.publish_did_document() {
            eprintln!("Warning: Failed to publish DID document: {}", e);
        }
    }

    Ok(())
}

async fn join_constellation(config: Config, constellation_id: String) -> Result<()> {
    info!("Joining constellation: {}", constellation_id);

    config.validate().map_err(|e| {
        eprintln!("Configuration validation failed: {}", e);
        e
    })?;

    let mut beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;

    beacon.join_constellation(&constellation_id).map_err(|e| {
        eprintln!("Failed to join constellation: {}", e);
        e
    })?;

    info!("Successfully joined constellation: {}", constellation_id);

    Ok(())
}

async fn show_status(config: Config) -> Result<()> {
    config.validate().map_err(|e| {
        eprintln!("Configuration validation failed: {}", e);
        e
    })?;

    let beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;

    println!("Astrum Beacon Status");
    println!("===================");
    println!("Local DID: {}", beacon.local_did());
    println!("Local Peer ID: {}", beacon.local_peer_id());
    println!("Data Directory: {:?}", beacon.config.beacon.data_dir);
    println!("Connected Peers: {}", beacon.connected_peers_count());
    println!("Constellations: {}", beacon.constellation_count());
    println!("Phonebook Entries: {}", beacon.phonebook().size());
    println!("Network Configuration:");
    println!("  Listen Addresses: {:?}", beacon.config.network.listen_addresses);
    println!("  Bootstrap Peers: {:?}", beacon.config.network.bootstrap_peers);
    println!("  mDNS Enabled: {}", beacon.config.network.enable_mdns);
    println!("  Relay Enabled: {}", beacon.config.network.enable_relay);

    Ok(())
}

async fn generate_config(output: PathBuf) -> Result<()> {
    let config = Config::default();
    config.save_to_file(&output)?;

    info!("Generated default configuration file: {}", output.display());

    Ok(())
}

async fn publish_did(config: Config) -> Result<()> {
    info!("Publishing DID document...");

    let mut beacon = Beacon::new(config).await?;
    beacon.publish_did_document()?;

    info!("DID document published for: {}", beacon.local_did());

    // Run briefly to allow the message to be sent
    tokio::time::timeout(std::time::Duration::from_secs(5), beacon.run()).await
        .unwrap_or_else(|_| {
            info!("DID publication completed");
            Ok(())
        })
}

async fn resolve_did(config: Config, did_string: String) -> Result<()> {
    info!("Resolving DID: {}", did_string);

    let did = astrum::AstrumDid::parse(&did_string)?;
    let mut beacon = Beacon::new(config).await?;

    beacon.resolve_did(&did)?;

    info!("DID resolution request sent for: {}", did);

    // Run briefly to allow resolution to complete
    tokio::time::timeout(std::time::Duration::from_secs(10), beacon.run()).await
        .unwrap_or_else(|_| {
            info!("DID resolution completed");
            Ok(())
        })
}

async fn show_did(config: Config) -> Result<()> {
    let beacon = Beacon::new(config).await.map_err(|e| {
        eprintln!("Failed to initialize beacon: {}", e);
        e
    })?;
    let did_document = beacon.get_local_did_document().map_err(|e| {
        eprintln!("Failed to get DID document: {}", e);
        e
    })?;

    println!("Local DID: {}", beacon.local_did());
    println!("Identity created: {}", chrono::DateTime::from_timestamp(beacon.identity_manager().identity().created_at() as i64, 0)
        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
        .unwrap_or_else(|| "Unknown".to_string()));
    if let Some(device_name) = beacon.identity_manager().identity().device_name() {
        println!("Device name: {}", device_name);
    }
    println!("DID Document:");
    println!("{}", serde_json::to_string_pretty(&did_document)?);

    Ok(())
}