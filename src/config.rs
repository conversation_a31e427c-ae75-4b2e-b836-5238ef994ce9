use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use crate::error::{AstrumError, Result};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub beacon: BeaconConfig,
    pub network: NetworkConfig,
    pub phonebook: PhonebookConfig,
    pub constellation: ConstellationConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BeaconConfig {
    pub name: Option<String>,
    pub data_dir: PathBuf,
    pub log_level: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub listen_addresses: Vec<String>,
    pub enable_mdns: bool,
    pub enable_relay: bool,
    pub relay_servers: Vec<String>,
    pub bootstrap_peers: Vec<String>,
    pub max_connections: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PhonebookConfig {
    pub persist_to_disk: bool,
    pub file_path: <PERSON><PERSON>uf,
    pub max_entries: usize,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ConstellationConfig {
    pub auto_join: bool,
    pub max_constellation_size: usize,
    pub trust_threshold: f64,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            beacon: BeaconConfig {
                name: None,
                data_dir: PathBuf::from("./astrum_data"),
                log_level: "info".to_string(),
            },
            network: NetworkConfig {
                listen_addresses: vec!["/ip4/0.0.0.0/tcp/0".to_string()],
                enable_mdns: true,
                enable_relay: true,
                relay_servers: vec![],
                bootstrap_peers: vec![],
                max_connections: 50,
            },
            phonebook: PhonebookConfig {
                persist_to_disk: true,
                file_path: PathBuf::from("./astrum_data/phonebook.json"),
                max_entries: 1000,
            },
            constellation: ConstellationConfig {
                auto_join: false,
                max_constellation_size: 10,
                trust_threshold: 0.7,
            },
        }
    }
}

impl Config {
    pub fn load_from_file(path: &PathBuf) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = toml::from_str(&content)?;
        Ok(config)
    }

    pub fn save_to_file(&self, path: &PathBuf) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| AstrumError::SerializationError(e.to_string()))?;
        std::fs::write(path, content)?;
        Ok(())
    }

    pub fn load_or_default(path: Option<&PathBuf>) -> Result<Self> {
        match path {
            Some(p) if p.exists() => Self::load_from_file(p),
            _ => Ok(Self::default()),
        }
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<()> {
        // Validate listen addresses
        for addr_str in &self.network.listen_addresses {
            addr_str.parse::<libp2p::Multiaddr>()
                .map_err(|e| AstrumError::ConfigError(format!("Invalid listen address '{}': {}", addr_str, e)))?;
        }

        // Validate bootstrap peers
        for peer_str in &self.network.bootstrap_peers {
            peer_str.parse::<libp2p::Multiaddr>()
                .map_err(|e| AstrumError::ConfigError(format!("Invalid bootstrap peer '{}': {}", peer_str, e)))?;
        }

        // Validate relay servers
        for relay_str in &self.network.relay_servers {
            relay_str.parse::<libp2p::Multiaddr>()
                .map_err(|e| AstrumError::ConfigError(format!("Invalid relay server '{}': {}", relay_str, e)))?;
        }

        // Validate trust threshold
        if self.constellation.trust_threshold < 0.0 || self.constellation.trust_threshold > 1.0 {
            return Err(AstrumError::ConfigError(
                "Trust threshold must be between 0.0 and 1.0".to_string()
            ));
        }

        // Validate max connections
        if self.network.max_connections == 0 {
            return Err(AstrumError::ConfigError(
                "Max connections must be greater than 0".to_string()
            ));
        }

        // Validate phonebook max entries
        if self.phonebook.max_entries == 0 {
            return Err(AstrumError::ConfigError(
                "Phonebook max entries must be greater than 0".to_string()
            ));
        }

        // Validate constellation max size
        if self.constellation.max_constellation_size == 0 {
            return Err(AstrumError::ConfigError(
                "Max constellation size must be greater than 0".to_string()
            ));
        }

        Ok(())
    }

    /// Merge configuration with CLI overrides
    pub fn merge_with_cli_args(
        &mut self,
        listen_addresses: Option<Vec<String>>,
        bootstrap_peers: Option<Vec<String>>,
        enable_relay: Option<bool>,
        enable_mdns: Option<bool>,
        data_dir: Option<PathBuf>,
    ) -> Result<()> {
        if let Some(addrs) = listen_addresses {
            if !addrs.is_empty() {
                self.network.listen_addresses = addrs;
            }
        }

        if let Some(peers) = bootstrap_peers {
            if !peers.is_empty() {
                self.network.bootstrap_peers = peers;
            }
        }

        if let Some(relay) = enable_relay {
            self.network.enable_relay = relay;
        }

        if let Some(mdns) = enable_mdns {
            self.network.enable_mdns = mdns;
        }

        if let Some(dir) = data_dir {
            self.beacon.data_dir = dir.clone();
            // Update dependent paths
            self.phonebook.file_path = dir.join("phonebook.json");
        }

        // Validate after merging
        self.validate()?;

        Ok(())
    }
}
