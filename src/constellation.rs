use libp2p::PeerId;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::time::{SystemTime, UNIX_EPOCH};
use crate::error::{AstrumError, Result};
// use crate::did::AstrumDid; // TODO: Use when implementing DID integration

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConstellationInfo {
    pub id: String,
    pub name: Option<String>,
    pub members: HashSet<PeerId>,
    pub created_at: u64,
    pub last_activity: u64,
    pub trust_threshold: f64,
    pub is_private: bool,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConstellationMembership {
    pub constellation_id: String,
    pub role: MemberRole,
    pub joined_at: u64,
    pub trust_score: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum MemberRole {
    Founder,
    Ad<PERSON>,
    Member,
    Observer,
}

#[derive(Debug)]
pub struct Constellation {
    constellations: HashMap<String, ConstellationInfo>,
    memberships: HashMap<PeerId, Vec<ConstellationMembership>>,
    local_peer_id: PeerId,
}

impl Constellation {
    pub fn new(local_peer_id: PeerId) -> Self {
        Self {
            constellations: HashMap::new(),
            memberships: HashMap::new(),
            local_peer_id,
        }
    }

    pub fn create_constellation(
        &mut self,
        name: Option<String>,
        is_private: bool,
        trust_threshold: f64,
    ) -> Result<String> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let constellation_id = format!("constellation_{}", now);
        
        let mut members = HashSet::new();
        members.insert(self.local_peer_id);

        let constellation_info = ConstellationInfo {
            id: constellation_id.clone(),
            name,
            members,
            created_at: now,
            last_activity: now,
            trust_threshold: trust_threshold.clamp(0.0, 1.0),
            is_private,
            metadata: HashMap::new(),
        };

        self.constellations.insert(constellation_id.clone(), constellation_info);

        // Add membership for local peer as founder
        let membership = ConstellationMembership {
            constellation_id: constellation_id.clone(),
            role: MemberRole::Founder,
            joined_at: now,
            trust_score: 1.0,
        };

        self.memberships
            .entry(self.local_peer_id)
            .or_insert_with(Vec::new)
            .push(membership);

        Ok(constellation_id)
    }

    pub fn join_constellation(
        &mut self,
        constellation_id: &str,
        peer_id: PeerId,
        role: MemberRole,
    ) -> Result<()> {
        let constellation = self.constellations
            .get_mut(constellation_id)
            .ok_or_else(|| AstrumError::ConstellationError(
                format!("Constellation {} not found", constellation_id)
            ))?;

        if constellation.members.contains(&peer_id) {
            return Err(AstrumError::ConstellationError(
                "Peer is already a member of this constellation".to_string()
            ));
        }

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        constellation.members.insert(peer_id);
        constellation.last_activity = now;

        let membership = ConstellationMembership {
            constellation_id: constellation_id.to_string(),
            role,
            joined_at: now,
            trust_score: 0.5, // Default neutral trust
        };

        self.memberships
            .entry(peer_id)
            .or_insert_with(Vec::new)
            .push(membership);

        Ok(())
    }

    pub fn leave_constellation(&mut self, constellation_id: &str, peer_id: PeerId) -> Result<()> {
        let constellation = self.constellations
            .get_mut(constellation_id)
            .ok_or_else(|| AstrumError::ConstellationError(
                format!("Constellation {} not found", constellation_id)
            ))?;

        constellation.members.remove(&peer_id);

        if let Some(memberships) = self.memberships.get_mut(&peer_id) {
            memberships.retain(|m| m.constellation_id != constellation_id);
            if memberships.is_empty() {
                self.memberships.remove(&peer_id);
            }
        }

        // If constellation is empty, remove it
        if constellation.members.is_empty() {
            self.constellations.remove(constellation_id);
        }

        Ok(())
    }

    pub fn get_constellation(&self, constellation_id: &str) -> Option<&ConstellationInfo> {
        self.constellations.get(constellation_id)
    }

    pub fn get_all_constellations(&self) -> &HashMap<String, ConstellationInfo> {
        &self.constellations
    }

    pub fn get_peer_memberships(&self, peer_id: &PeerId) -> Option<&Vec<ConstellationMembership>> {
        self.memberships.get(peer_id)
    }

    pub fn get_constellation_members(&self, constellation_id: &str) -> Option<&HashSet<PeerId>> {
        self.constellations.get(constellation_id).map(|c| &c.members)
    }

    pub fn update_member_trust_score(
        &mut self,
        constellation_id: &str,
        peer_id: &PeerId,
        trust_score: f64,
    ) -> Result<()> {
        if let Some(memberships) = self.memberships.get_mut(peer_id) {
            for membership in memberships.iter_mut() {
                if membership.constellation_id == constellation_id {
                    membership.trust_score = trust_score.clamp(0.0, 1.0);
                    return Ok(());
                }
            }
        }
        
        Err(AstrumError::ConstellationError(
            "Membership not found".to_string()
        ))
    }

    pub fn get_trusted_members(&self, constellation_id: &str) -> Vec<PeerId> {
        if let Some(constellation) = self.constellations.get(constellation_id) {
            constellation.members
                .iter()
                .filter(|peer_id| {
                    if let Some(memberships) = self.memberships.get(peer_id) {
                        memberships.iter().any(|m| {
                            m.constellation_id == constellation_id 
                                && m.trust_score >= constellation.trust_threshold
                        })
                    } else {
                        false
                    }
                })
                .copied()
                .collect()
        } else {
            Vec::new()
        }
    }

    pub fn is_member(&self, constellation_id: &str, peer_id: &PeerId) -> bool {
        self.constellations
            .get(constellation_id)
            .map(|c| c.members.contains(peer_id))
            .unwrap_or(false)
    }

    pub fn update_activity(&mut self, constellation_id: &str) -> Result<()> {
        if let Some(constellation) = self.constellations.get_mut(constellation_id) {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();
            constellation.last_activity = now;
            Ok(())
        } else {
            Err(AstrumError::ConstellationError(
                format!("Constellation {} not found", constellation_id)
            ))
        }
    }

    pub fn cleanup_inactive_constellations(&mut self, max_inactive_seconds: u64) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let inactive_constellations: Vec<String> = self.constellations
            .iter()
            .filter(|(_, info)| now - info.last_activity > max_inactive_seconds)
            .map(|(id, _)| id.clone())
            .collect();

        for constellation_id in inactive_constellations {
            if let Some(constellation) = self.constellations.remove(&constellation_id) {
                // Remove memberships for all members
                for member in constellation.members {
                    if let Some(memberships) = self.memberships.get_mut(&member) {
                        memberships.retain(|m| m.constellation_id != constellation_id);
                        if memberships.is_empty() {
                            self.memberships.remove(&member);
                        }
                    }
                }
            }
        }

        Ok(())
    }
}
