use std::fmt;

pub type Result<T> = std::result::Result<T, AstrumError>;

#[derive(Debug)]
pub enum AstrumError {
    NetworkError(String),
    ConfigError(String),
    PhonebookError(String),
    ConstellationError(String),
    Did<PERSON>rror(String),
    IoError(std::io::Error),
    SerializationError(String),
    LibP2pError(String),
}

impl fmt::Display for AstrumError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AstrumError::NetworkError(msg) => write!(f, "Network error: {}", msg),
            AstrumError::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
            AstrumError::PhonebookError(msg) => write!(f, "Phonebook error: {}", msg),
            AstrumError::ConstellationError(msg) => write!(f, "Constellation error: {}", msg),
            AstrumError::DidError(msg) => write!(f, "DID error: {}", msg),
            AstrumError::IoError(err) => write!(f, "IO error: {}", err),
            AstrumError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            AstrumError::LibP2pError(msg) => write!(f, "libp2p error: {}", msg),
        }
    }
}

impl std::error::Error for AstrumError {}

impl From<std::io::Error> for AstrumError {
    fn from(err: std::io::Error) -> Self {
        AstrumError::IoError(err)
    }
}

impl From<serde_json::Error> for AstrumError {
    fn from(err: serde_json::Error) -> Self {
        AstrumError::SerializationError(err.to_string())
    }
}

impl From<toml::de::Error> for AstrumError {
    fn from(err: toml::de::Error) -> Self {
        AstrumError::ConfigError(err.to_string())
    }
}

impl From<libp2p::noise::Error> for AstrumError {
    fn from(err: libp2p::noise::Error) -> Self {
        AstrumError::LibP2pError(err.to_string())
    }
}

impl From<libp2p::TransportError<std::io::Error>> for AstrumError {
    fn from(err: libp2p::TransportError<std::io::Error>) -> Self {
        AstrumError::NetworkError(err.to_string())
    }
}
