use astrum::mobile::cli::{MobileCli, handle_mobile_command};
use clap::Parser;
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging for mobile
    tracing_subscriber::fmt()
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .compact()
        .init();

    // Parse mobile CLI arguments
    let cli = MobileCli::parse();
    
    // Handle mobile command
    if let Err(e) = handle_mobile_command(cli).await {
        eprintln!("Error: {}", e);
        std::process::exit(1);
    }

    Ok(())
}
