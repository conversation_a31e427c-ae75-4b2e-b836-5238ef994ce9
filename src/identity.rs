use libp2p::{identity, PeerId};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use crate::error::{AstrumError, Result};
use crate::did::AstrumDid;

/// Device identity that persists across restarts
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeviceIdentity {
    /// The libp2p keypair (serialized)
    keypair_bytes: Vec<u8>,
    /// The peer ID derived from the keypair
    peer_id: PeerId,
    /// The Astrum DID for this device
    did: AstrumDid,
    /// When this identity was created
    created_at: u64,
    /// Device name/hostname when created
    device_name: Option<String>,
}

impl DeviceIdentity {
    /// Create a new device identity
    pub fn new() -> Result<Self> {
        let keypair = identity::Keypair::generate_ed25519();
        let peer_id = PeerId::from(keypair.public());
        let did = AstrumDid::new(peer_id);
        
        let keypair_bytes = keypair.to_protobuf_encoding()
            .map_err(|e| AstrumError::DidError(format!("Failed to serialize keypair: {}", e)))?;
        
        let created_at = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let device_name = hostname::get()
            .ok()
            .and_then(|h| h.into_string().ok());
        
        Ok(DeviceIdentity {
            keypair_bytes,
            peer_id,
            did,
            created_at,
            device_name,
        })
    }
    
    /// Load device identity from file, or create new if doesn't exist
    pub fn load_or_create<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        
        if path.exists() {
            Self::load_from_file(path)
        } else {
            let identity = Self::new()?;
            identity.save_to_file(path)?;
            Ok(identity)
        }
    }
    
    /// Load device identity from file
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref())?;
        let identity: DeviceIdentity = serde_json::from_str(&content)?;
        
        // Validate that the keypair matches the stored peer_id
        let keypair = identity.get_keypair()?;
        let derived_peer_id = PeerId::from(keypair.public());
        
        if derived_peer_id != identity.peer_id {
            return Err(AstrumError::DidError(
                "Identity file corrupted: keypair doesn't match peer_id".to_string()
            ));
        }
        
        Ok(identity)
    }
    
    /// Save device identity to file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();
        
        // Create parent directory if it doesn't exist
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent)?;
        }
        
        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        
        // Set restrictive permissions (owner read/write only)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(path)?.permissions();
            perms.set_mode(0o600); // rw-------
            fs::set_permissions(path, perms)?;
        }
        
        Ok(())
    }
    
    /// Get the libp2p keypair
    pub fn get_keypair(&self) -> Result<identity::Keypair> {
        identity::Keypair::from_protobuf_encoding(&self.keypair_bytes)
            .map_err(|e| AstrumError::DidError(format!("Failed to deserialize keypair: {}", e)))
    }
    
    /// Get the peer ID
    pub fn peer_id(&self) -> &PeerId {
        &self.peer_id
    }
    
    /// Get the DID
    pub fn did(&self) -> &AstrumDid {
        &self.did
    }
    
    /// Get creation timestamp
    pub fn created_at(&self) -> u64 {
        self.created_at
    }
    
    /// Get device name
    pub fn device_name(&self) -> Option<&str> {
        self.device_name.as_deref()
    }
    
    /// Get the default identity file path for the current user
    pub fn default_identity_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| AstrumError::ConfigError("Cannot determine config directory".to_string()))?;
        
        Ok(config_dir.join("astrum").join("identity.json"))
    }
    
    /// Get the system-wide identity path (for daemon mode)
    pub fn system_identity_path() -> PathBuf {
        PathBuf::from("/var/lib/astrum/identity.json")
    }
    
    /// Check if this identity file exists and is valid
    pub fn validate_identity_file<P: AsRef<Path>>(path: P) -> Result<bool> {
        let path = path.as_ref();
        
        if !path.exists() {
            return Ok(false);
        }
        
        match Self::load_from_file(path) {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}

/// Identity manager for handling device identity across the application
pub struct IdentityManager {
    identity: DeviceIdentity,
    identity_path: PathBuf,
}

impl IdentityManager {
    /// Create a new identity manager with user-specific identity
    pub fn new_user() -> Result<Self> {
        let identity_path = DeviceIdentity::default_identity_path()?;
        let identity = DeviceIdentity::load_or_create(&identity_path)?;
        
        Ok(IdentityManager {
            identity,
            identity_path,
        })
    }
    
    /// Create a new identity manager with system-wide identity (for daemon)
    pub fn new_system() -> Result<Self> {
        let identity_path = DeviceIdentity::system_identity_path();
        let identity = DeviceIdentity::load_or_create(&identity_path)?;
        
        Ok(IdentityManager {
            identity,
            identity_path,
        })
    }
    
    /// Create identity manager with custom path
    pub fn new_with_path<P: AsRef<Path>>(path: P) -> Result<Self> {
        let identity_path = path.as_ref().to_path_buf();
        let identity = DeviceIdentity::load_or_create(&identity_path)?;
        
        Ok(IdentityManager {
            identity,
            identity_path,
        })
    }
    
    /// Get the device identity
    pub fn identity(&self) -> &DeviceIdentity {
        &self.identity
    }
    
    /// Get the identity file path
    pub fn identity_path(&self) -> &Path {
        &self.identity_path
    }
    
    /// Refresh identity from disk (in case it was updated externally)
    pub fn refresh(&mut self) -> Result<()> {
        self.identity = DeviceIdentity::load_from_file(&self.identity_path)?;
        Ok(())
    }
}
