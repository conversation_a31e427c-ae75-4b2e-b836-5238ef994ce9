use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use tokio::net::{UnixStream, UnixListener};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tracing::{info, error, debug};

use crate::{Result, AstrumError, AstrumDid};

/// Client for communicating with the Astrum daemon
pub struct AstrumClient {
    socket_path: PathBuf,
}

impl AstrumClient {
    /// Create a new client
    pub fn new(system_mode: bool) -> Self {
        let socket_path = if system_mode {
            PathBuf::from("/var/run/astrum/astrum.sock")
        } else {
            dirs::runtime_dir()
                .unwrap_or_else(|| PathBuf::from("/tmp"))
                .join("astrum")
                .join("astrum.sock")
        };

        AstrumClient { socket_path }
    }

    /// Send a command to the daemon
    pub async fn send_command(&self, command: ClientCommand) -> Result<ClientResponse> {
        let mut stream = UnixStream::connect(&self.socket_path).await
            .map_err(|e| AstrumError::NetworkError(format!("Failed to connect to daemon: {}", e)))?;

        let command_json = serde_json::to_string(&command)?;
        stream.write_all(command_json.as_bytes()).await
            .map_err(|e| AstrumError::IoError(e))?;
        stream.shutdown().await
            .map_err(|e| AstrumError::IoError(e))?;

        let mut response = String::new();
        stream.read_to_string(&mut response).await
            .map_err(|e| AstrumError::IoError(e))?;

        let response: ClientResponse = serde_json::from_str(&response)?;
        Ok(response)
    }

    /// Get daemon status
    pub async fn status(&self) -> Result<DaemonInfo> {
        let response = self.send_command(ClientCommand::Status).await?;
        match response {
            ClientResponse::Status(info) => Ok(info),
            ClientResponse::Error(msg) => Err(AstrumError::NetworkError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }

    /// Create a constellation
    pub async fn create_constellation(&self, name: Option<String>, private: bool) -> Result<String> {
        let response = self.send_command(ClientCommand::CreateConstellation { name, private }).await?;
        match response {
            ClientResponse::ConstellationCreated(id) => Ok(id),
            ClientResponse::Error(msg) => Err(AstrumError::ConstellationError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }

    /// Join a constellation
    pub async fn join_constellation(&self, constellation_id: String) -> Result<()> {
        let response = self.send_command(ClientCommand::JoinConstellation { constellation_id }).await?;
        match response {
            ClientResponse::Success => Ok(()),
            ClientResponse::Error(msg) => Err(AstrumError::ConstellationError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }

    /// Connect to a peer
    pub async fn connect_peer(&self, address: String) -> Result<()> {
        let response = self.send_command(ClientCommand::ConnectPeer { address }).await?;
        match response {
            ClientResponse::Success => Ok(()),
            ClientResponse::Error(msg) => Err(AstrumError::NetworkError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }

    /// Publish DID document
    pub async fn publish_did(&self) -> Result<()> {
        let response = self.send_command(ClientCommand::PublishDid).await?;
        match response {
            ClientResponse::Success => Ok(()),
            ClientResponse::Error(msg) => Err(AstrumError::DidError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }

    /// Resolve a DID
    pub async fn resolve_did(&self, did: String) -> Result<String> {
        let response = self.send_command(ClientCommand::ResolveDid { did }).await?;
        match response {
            ClientResponse::DidResolved(document) => Ok(document),
            ClientResponse::Error(msg) => Err(AstrumError::DidError(msg)),
            _ => Err(AstrumError::NetworkError("Unexpected response".to_string())),
        }
    }
}

/// Commands that can be sent to the daemon
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClientCommand {
    Status,
    CreateConstellation { name: Option<String>, private: bool },
    JoinConstellation { constellation_id: String },
    ConnectPeer { address: String },
    PublishDid,
    ResolveDid { did: String },
    Shutdown,
}

/// Responses from the daemon
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClientResponse {
    Status(DaemonInfo),
    ConstellationCreated(String),
    DidResolved(String),
    Success,
    Error(String),
}

/// Daemon information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonInfo {
    pub is_running: bool,
    pub pid: Option<u32>,
    pub local_did: AstrumDid,
    pub local_peer_id: String,
    pub connected_peers: usize,
    pub constellations: usize,
    pub phonebook_entries: usize,
    pub uptime_seconds: Option<u64>,
}

impl std::fmt::Display for DaemonInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Astrum Daemon Status")?;
        writeln!(f, "===================")?;
        writeln!(f, "Running: {}", if self.is_running { "Yes" } else { "No" })?;
        if let Some(pid) = self.pid {
            writeln!(f, "PID: {}", pid)?;
        }
        writeln!(f, "Local DID: {}", self.local_did)?;
        writeln!(f, "Local Peer ID: {}", self.local_peer_id)?;
        writeln!(f, "Connected Peers: {}", self.connected_peers)?;
        writeln!(f, "Constellations: {}", self.constellations)?;
        writeln!(f, "Phonebook Entries: {}", self.phonebook_entries)?;
        if let Some(uptime) = self.uptime_seconds {
            let hours = uptime / 3600;
            let minutes = (uptime % 3600) / 60;
            let seconds = uptime % 60;
            writeln!(f, "Uptime: {}h {}m {}s", hours, minutes, seconds)?;
        }
        Ok(())
    }
}

/// Server for handling client connections to the daemon
pub struct ClientServer {
    listener: UnixListener,
    beacon: std::sync::Arc<tokio::sync::RwLock<crate::Beacon>>,
}

impl ClientServer {
    /// Create a new client server
    pub async fn new(
        socket_path: PathBuf,
        beacon: std::sync::Arc<tokio::sync::RwLock<crate::Beacon>>,
    ) -> Result<Self> {
        // Remove existing socket file
        if socket_path.exists() {
            std::fs::remove_file(&socket_path)?;
        }

        // Create parent directory
        if let Some(parent) = socket_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let listener = UnixListener::bind(&socket_path)
            .map_err(|e| AstrumError::IoError(e))?;

        info!("Client server listening on: {}", socket_path.display());

        Ok(ClientServer { listener, beacon })
    }

    /// Start the client server
    pub async fn start(&self) -> Result<()> {
        loop {
            match self.listener.accept().await {
                Ok((stream, _)) => {
                    // For now, handle clients synchronously to avoid Send/Sync issues
                    if let Err(e) = Self::handle_client(stream, self.beacon.clone()).await {
                        error!("Error handling client: {}", e);
                    }
                }
                Err(e) => {
                    error!("Error accepting client connection: {}", e);
                }
            }
        }
    }

    /// Handle a client connection
    async fn handle_client(
        mut stream: UnixStream,
        beacon: std::sync::Arc<tokio::sync::RwLock<crate::Beacon>>,
    ) -> Result<()> {
        let mut buffer = String::new();
        stream.read_to_string(&mut buffer).await
            .map_err(|e| AstrumError::IoError(e))?;

        let command: ClientCommand = serde_json::from_str(&buffer)?;
        debug!("Received command: {:?}", command);

        let response = Self::process_command(command, beacon).await;
        let response_json = serde_json::to_string(&response)?;

        stream.write_all(response_json.as_bytes()).await
            .map_err(|e| AstrumError::IoError(e))?;

        Ok(())
    }

    /// Process a client command
    async fn process_command(
        command: ClientCommand,
        beacon: std::sync::Arc<tokio::sync::RwLock<crate::Beacon>>,
    ) -> ClientResponse {
        match command {
            ClientCommand::Status => {
                let beacon = beacon.read().await;
                let info = DaemonInfo {
                    is_running: true,
                    pid: Some(std::process::id()),
                    local_did: beacon.local_did().clone(),
                    local_peer_id: beacon.local_peer_id().to_string(),
                    connected_peers: beacon.connected_peers_count(),
                    constellations: beacon.constellation_count(),
                    phonebook_entries: beacon.phonebook().size(),
                    uptime_seconds: None, // TODO: Track uptime
                };
                ClientResponse::Status(info)
            }
            ClientCommand::CreateConstellation { name, private } => {
                let mut beacon = beacon.write().await;
                match beacon.create_constellation(name, private) {
                    Ok(id) => ClientResponse::ConstellationCreated(id),
                    Err(e) => ClientResponse::Error(e.to_string()),
                }
            }
            ClientCommand::JoinConstellation { constellation_id } => {
                let mut beacon = beacon.write().await;
                match beacon.join_constellation(&constellation_id) {
                    Ok(()) => ClientResponse::Success,
                    Err(e) => ClientResponse::Error(e.to_string()),
                }
            }
            ClientCommand::ConnectPeer { address } => {
                let mut beacon = beacon.write().await;
                match address.parse() {
                    Ok(addr) => match beacon.connect_to_peer(addr).await {
                        Ok(()) => ClientResponse::Success,
                        Err(e) => ClientResponse::Error(e.to_string()),
                    },
                    Err(e) => ClientResponse::Error(format!("Invalid address: {}", e)),
                }
            }
            ClientCommand::PublishDid => {
                let mut beacon = beacon.write().await;
                match beacon.publish_did_document() {
                    Ok(()) => ClientResponse::Success,
                    Err(e) => ClientResponse::Error(e.to_string()),
                }
            }
            ClientCommand::ResolveDid { did } => {
                // TODO: Implement DID resolution
                ClientResponse::Error("DID resolution not yet implemented".to_string())
            }
            ClientCommand::Shutdown => {
                info!("Received shutdown command");
                std::process::exit(0);
            }
        }
    }
}
