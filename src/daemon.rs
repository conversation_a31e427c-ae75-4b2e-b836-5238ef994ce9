use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error, warn};

use crate::{Beacon, Config, Result, AstrumError, IdentityManager};

/// Astrum daemon that runs the beacon in the background
pub struct AstrumDaemon {
    beacon: Arc<RwLock<Beacon>>,
    config: Config,
    pid_file: Option<PathBuf>,
}

impl AstrumDaemon {
    /// Create a new daemon instance
    pub async fn new(config: Config, system_mode: bool) -> Result<Self> {
        let beacon = if system_mode {
            Beacon::new_system_daemon(config.clone()).await?
        } else {
            Beacon::new(config.clone()).await?
        };

        let pid_file = if system_mode {
            Some(PathBuf::from("/var/run/astrum/astrum.pid"))
        } else {
            dirs::runtime_dir().map(|d| d.join("astrum").join("astrum.pid"))
        };

        Ok(AstrumDaemon {
            beacon: Arc::new(RwLock::new(beacon)),
            config,
            pid_file,
        })
    }

    /// Start the daemon
    pub async fn start(&self) -> Result<()> {
        info!("Starting Astrum daemon");

        // Write PID file
        if let Some(pid_file) = &self.pid_file {
            self.write_pid_file(pid_file)?;
        }

        // Set up signal handlers
        let mut sigterm = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .map_err(|e| AstrumError::IoError(e))?;
        let mut sigint = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::interrupt())
            .map_err(|e| AstrumError::IoError(e))?;
        let mut sighup = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::hangup())
            .map_err(|e| AstrumError::IoError(e))?;

        let beacon = self.beacon.clone();

        tokio::select! {
            result = async {
                let mut beacon = beacon.write().await;
                beacon.run().await
            } => {
                match result {
                    Ok(()) => info!("Beacon stopped gracefully"),
                    Err(e) => {
                        error!("Beacon error: {}", e);
                        return Err(e);
                    }
                }
            }
            _ = sigterm.recv() => {
                info!("Received SIGTERM, shutting down gracefully...");
            }
            _ = sigint.recv() => {
                info!("Received SIGINT, shutting down gracefully...");
            }
            _ = sighup.recv() => {
                info!("Received SIGHUP, reloading configuration...");
                if let Err(e) = self.reload_config().await {
                    warn!("Failed to reload configuration: {}", e);
                }
            }
        }

        // Cleanup
        if let Some(pid_file) = &self.pid_file {
            let _ = std::fs::remove_file(pid_file);
        }

        Ok(())
    }

    /// Stop the daemon
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping Astrum daemon");

        // In a real implementation, this would send a signal to the running daemon
        // For now, we'll just clean up the PID file
        if let Some(pid_file) = &self.pid_file {
            if pid_file.exists() {
                std::fs::remove_file(pid_file)?;
            }
        }

        Ok(())
    }

    /// Get daemon status
    pub async fn status(&self) -> Result<DaemonStatus> {
        let is_running = if let Some(pid_file) = &self.pid_file {
            self.is_daemon_running(pid_file).await?
        } else {
            false
        };

        let beacon = self.beacon.read().await;
        
        Ok(DaemonStatus {
            is_running,
            pid: self.get_daemon_pid().await?,
            local_did: beacon.local_did().clone(),
            local_peer_id: *beacon.local_peer_id(),
            connected_peers: beacon.connected_peers_count(),
            constellations: beacon.constellation_count(),
            phonebook_entries: beacon.phonebook().size(),
            uptime: None, // TODO: Track uptime
        })
    }

    /// Reload configuration
    async fn reload_config(&self) -> Result<()> {
        info!("Reloading configuration");
        // TODO: Implement configuration reloading
        Ok(())
    }

    /// Write PID file
    fn write_pid_file(&self, pid_file: &PathBuf) -> Result<()> {
        if let Some(parent) = pid_file.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let pid = std::process::id();
        std::fs::write(pid_file, pid.to_string())?;

        info!("PID file written: {} (PID: {})", pid_file.display(), pid);
        Ok(())
    }

    /// Check if daemon is running
    async fn is_daemon_running(&self, pid_file: &PathBuf) -> Result<bool> {
        if !pid_file.exists() {
            return Ok(false);
        }

        let pid_str = std::fs::read_to_string(pid_file)?;
        let pid: u32 = pid_str.trim().parse()
            .map_err(|_| AstrumError::ConfigError("Invalid PID in PID file".to_string()))?;

        // Check if process is still running
        Ok(self.is_process_running(pid))
    }

    /// Check if a process is running
    fn is_process_running(&self, pid: u32) -> bool {
        #[cfg(unix)]
        {
            use std::process::Command;
            Command::new("kill")
                .args(["-0", &pid.to_string()])
                .output()
                .map(|output| output.status.success())
                .unwrap_or(false)
        }
        #[cfg(not(unix))]
        {
            // For non-Unix systems, we'd need a different approach
            false
        }
    }

    /// Get daemon PID
    async fn get_daemon_pid(&self) -> Result<Option<u32>> {
        if let Some(pid_file) = &self.pid_file {
            if pid_file.exists() {
                let pid_str = std::fs::read_to_string(pid_file)?;
                let pid: u32 = pid_str.trim().parse()
                    .map_err(|_| AstrumError::ConfigError("Invalid PID in PID file".to_string()))?;
                return Ok(Some(pid));
            }
        }
        Ok(None)
    }
}

/// Daemon status information
#[derive(Debug, Clone)]
pub struct DaemonStatus {
    pub is_running: bool,
    pub pid: Option<u32>,
    pub local_did: crate::AstrumDid,
    pub local_peer_id: libp2p::PeerId,
    pub connected_peers: usize,
    pub constellations: usize,
    pub phonebook_entries: usize,
    pub uptime: Option<std::time::Duration>,
}

impl std::fmt::Display for DaemonStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Astrum Daemon Status")?;
        writeln!(f, "===================")?;
        writeln!(f, "Running: {}", if self.is_running { "Yes" } else { "No" })?;
        if let Some(pid) = self.pid {
            writeln!(f, "PID: {}", pid)?;
        }
        writeln!(f, "Local DID: {}", self.local_did)?;
        writeln!(f, "Local Peer ID: {}", self.local_peer_id)?;
        writeln!(f, "Connected Peers: {}", self.connected_peers)?;
        writeln!(f, "Constellations: {}", self.constellations)?;
        writeln!(f, "Phonebook Entries: {}", self.phonebook_entries)?;
        if let Some(uptime) = self.uptime {
            writeln!(f, "Uptime: {:?}", uptime)?;
        }
        Ok(())
    }
}
