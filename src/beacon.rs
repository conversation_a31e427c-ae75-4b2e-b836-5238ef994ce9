use libp2p::{
    identity, PeerId, Multiaddr, Transport,
    swarm::{Swarm, SwarmEvent, SwarmBuilder},
    tcp, noise, yamux,
};
use futures::stream::StreamExt;
use std::time::Duration;
use tokio::time;
use tracing::{info, warn, error, debug};

use crate::config::Config;
use crate::error::{AstrumError, Result};
use crate::phonebook::{Phonebook, ConnectionStatus};
use crate::constellation::Constellation;
use crate::protocols::{AstrumBehaviour, constants};
use crate::did::AstrumDid;

type AstrumSwarmEvent = SwarmEvent<crate::protocols::AstrumBehaviourEvent, libp2p::swarm::THandlerErr<AstrumBehaviour>>;

/// The main Astrum Beacon - the fundamental node in the Astrum network
pub struct Beacon {
    swarm: Swarm<AstrumBehaviour>,
    pub config: Config,
    phonebook: Phonebook,
    constellation: Constellation,
    local_peer_id: PeerId,
    local_did: AstrumDid,
    keypair: identity::Keypair,
}

impl Beacon {
    /// Create a new Beacon instance
    pub async fn new(config: Config) -> Result<Self> {
        // Generate or load identity
        let keypair = identity::Keypair::generate_ed25519();
        let local_peer_id = PeerId::from(keypair.public());
        let local_did = AstrumDid::new(local_peer_id);

        info!("Beacon starting with PeerId: {}", local_peer_id);
        info!("Beacon DID: {}", local_did);

        // Create data directory
        std::fs::create_dir_all(&config.beacon.data_dir)?;

        // Initialize phonebook
        let phonebook_path = if config.phonebook.persist_to_disk {
            Some(config.phonebook.file_path.clone())
        } else {
            None
        };
        let mut phonebook = Phonebook::new(phonebook_path, config.phonebook.max_entries);
        phonebook.load_from_disk()?;

        // Initialize constellation manager
        let constellation = Constellation::new(local_peer_id);

        // Build transport
        let transport = tcp::tokio::Transport::new(tcp::Config::default().nodelay(true))
            .upgrade(libp2p::core::upgrade::Version::V1)
            .authenticate(noise::Config::new(&keypair)?)
            .multiplex(yamux::Config::default())
            .boxed();

        // Create network behaviour
        let behaviour = AstrumBehaviour::new(
            local_peer_id,
            &keypair,
            config.network.enable_mdns,
        ).map_err(|e| AstrumError::NetworkError(e.to_string()))?;

        // Build swarm
        let mut swarm = SwarmBuilder::with_tokio_executor(transport, behaviour, local_peer_id)
            .build();

        // Configure listen addresses
        for addr_str in &config.network.listen_addresses {
            let addr: Multiaddr = addr_str.parse()
                .map_err(|e| AstrumError::NetworkError(format!("Invalid listen address {}: {}", addr_str, e)))?;
            swarm.listen_on(addr)?;
        }

        // Subscribe to default topics
        let beacon_discovery_topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        swarm.behaviour_mut().subscribe_to_topic(&beacon_discovery_topic)
            .map_err(|e| AstrumError::NetworkError(e.to_string()))?;

        Ok(Beacon {
            swarm,
            config,
            phonebook,
            constellation,
            local_peer_id,
            local_did,
            keypair,
        })
    }

    /// Get the local peer ID
    pub fn local_peer_id(&self) -> &PeerId {
        &self.local_peer_id
    }

    /// Get the local DID
    pub fn local_did(&self) -> &AstrumDid {
        &self.local_did
    }

    /// Get a reference to the phonebook
    pub fn phonebook(&self) -> &Phonebook {
        &self.phonebook
    }

    /// Get a mutable reference to the phonebook
    pub fn phonebook_mut(&mut self) -> &mut Phonebook {
        &mut self.phonebook
    }

    /// Get a reference to the constellation manager
    pub fn constellation(&self) -> &Constellation {
        &self.constellation
    }

    /// Get a mutable reference to the constellation manager
    pub fn constellation_mut(&mut self) -> &mut Constellation {
        &mut self.constellation
    }

    /// Connect to a peer by address
    pub fn dial(&mut self, addr: Multiaddr) -> Result<()> {
        self.swarm.dial(addr.clone())
            .map_err(|e| AstrumError::NetworkError(format!("Failed to dial {}: {}", addr, e)))?;
        Ok(())
    }

    /// Connect to bootstrap peers
    pub async fn connect_to_bootstrap_peers(&mut self) -> Result<()> {
        for peer_addr in &self.config.network.bootstrap_peers.clone() {
            let addr: Multiaddr = peer_addr.parse()
                .map_err(|e| AstrumError::NetworkError(format!("Invalid bootstrap address {}: {}", peer_addr, e)))?;
            
            info!("Connecting to bootstrap peer: {}", addr);
            self.dial(addr)?;
        }
        Ok(())
    }

    /// Start the main event loop
    pub async fn run(&mut self) -> Result<()> {
        info!("Beacon started and listening for connections");

        // Connect to bootstrap peers
        self.connect_to_bootstrap_peers().await?;

        // Start periodic tasks
        let mut cleanup_interval = time::interval(Duration::from_secs(300)); // 5 minutes
        let mut announce_interval = time::interval(Duration::from_secs(60)); // 1 minute

        loop {
            tokio::select! {
                // Handle swarm events
                event = self.swarm.select_next_some() => {
                    if let Err(e) = self.handle_swarm_event(event).await {
                        error!("Error handling swarm event: {}", e);
                    }
                }
                
                // Periodic cleanup
                _ = cleanup_interval.tick() => {
                    if let Err(e) = self.periodic_cleanup().await {
                        error!("Error during periodic cleanup: {}", e);
                    }
                }
                
                // Periodic announcements
                _ = announce_interval.tick() => {
                    if let Err(e) = self.announce_presence().await {
                        error!("Error announcing presence: {}", e);
                    }
                }
            }
        }
    }

    /// Handle swarm events
    async fn handle_swarm_event(&mut self, event: AstrumSwarmEvent) -> Result<()> {
        match event {
            SwarmEvent::NewListenAddr { address, .. } => {
                info!("Listening on {}", address);
            }
            SwarmEvent::ConnectionEstablished { peer_id, endpoint, .. } => {
                info!("Connected to peer: {}", peer_id);
                self.phonebook.update_peer_status(&peer_id, ConnectionStatus::Connected)?;

                // Add peer to DHT
                let addr = endpoint.get_remote_address().clone();
                self.swarm.behaviour_mut().add_peer_to_dht(peer_id, addr);
            }
            SwarmEvent::ConnectionClosed { peer_id, cause, .. } => {
                info!("Connection closed with peer {}: {:?}", peer_id, cause);
                self.phonebook.update_peer_status(&peer_id, ConnectionStatus::Disconnected)?;
            }
            SwarmEvent::IncomingConnection { .. } => {
                debug!("Incoming connection");
            }
            SwarmEvent::OutgoingConnectionError { peer_id, error, .. } => {
                if let Some(peer_id) = peer_id {
                    warn!("Outgoing connection error to {}: {}", peer_id, error);
                    self.phonebook.update_peer_status(&peer_id, ConnectionStatus::Failed)?;
                }
            }
            SwarmEvent::Behaviour(event) => {
                debug!("Behaviour event: {:?}", event);
                self.handle_behaviour_event(event).await?;
            }
            _ => {}
        }
        Ok(())
    }

    /// Handle behaviour-specific events
    async fn handle_behaviour_event(&mut self, event: crate::protocols::AstrumBehaviourEvent) -> Result<()> {
        use crate::protocols::AstrumBehaviourEvent;

        match event {
            AstrumBehaviourEvent::Gossipsub(gossipsub_event) => {
                self.handle_gossipsub_event(gossipsub_event).await?;
            }
            AstrumBehaviourEvent::Kademlia(kad_event) => {
                self.handle_kademlia_event(kad_event).await?;
            }
            AstrumBehaviourEvent::Mdns(mdns_event) => {
                self.handle_mdns_event(mdns_event).await?;
            }
            AstrumBehaviourEvent::Ping(_) => {
                // Ping events are mostly for debugging
            }
            AstrumBehaviourEvent::Identify(identify_event) => {
                self.handle_identify_event(identify_event).await?;
            }
            AstrumBehaviourEvent::RelayClient(relay_event) => {
                self.handle_relay_event(relay_event).await?;
            }
            AstrumBehaviourEvent::Dcutr(dcutr_event) => {
                self.handle_dcutr_event(dcutr_event).await?;
            }
        }
        Ok(())
    }

    /// Handle Gossipsub events
    async fn handle_gossipsub_event(&mut self, event: libp2p::gossipsub::Event) -> Result<()> {
        use libp2p::gossipsub::Event;

        match event {
            Event::Message { message, .. } => {
                debug!("Received gossipsub message from {:?}", message.source);

                // Try to parse as Astrum message
                if let Ok(astrum_message) = serde_json::from_slice::<crate::protocols::messages::AstrumMessage>(&message.data) {
                    self.handle_astrum_message(astrum_message, message.source).await?;
                }
            }
            Event::Subscribed { peer_id, topic } => {
                info!("Peer {} subscribed to topic {}", peer_id, topic);
            }
            Event::Unsubscribed { peer_id, topic } => {
                info!("Peer {} unsubscribed from topic {}", peer_id, topic);
            }
            _ => {}
        }
        Ok(())
    }

    /// Handle Astrum protocol messages
    async fn handle_astrum_message(
        &mut self,
        message: crate::protocols::messages::AstrumMessage,
        source: Option<libp2p::PeerId>
    ) -> Result<()> {
        use crate::protocols::messages::AstrumMessage;

        match message {
            AstrumMessage::BeaconAnnouncement(announcement) => {
                if let Some(peer_id) = source {
                    info!("Received beacon announcement from {}", peer_id);

                    // Parse addresses and add to phonebook
                    let addresses: Vec<libp2p::Multiaddr> = announcement.addresses
                        .iter()
                        .filter_map(|addr_str| addr_str.parse().ok())
                        .collect();

                    self.phonebook.add_peer(peer_id, addresses)?;
                }
            }
            AstrumMessage::ConstellationInvite(invite) => {
                info!("Received constellation invite for {}", invite.constellation_id);
                // TODO: Handle constellation invite logic
            }
            AstrumMessage::ConstellationJoin(join) => {
                info!("Peer joined constellation {}", join.constellation_id);
                // TODO: Handle constellation join logic
            }
            AstrumMessage::ConstellationLeave(leave) => {
                info!("Peer left constellation {}", leave.constellation_id);
                // TODO: Handle constellation leave logic
            }
            AstrumMessage::DidResolution(resolution) => {
                info!("Received DID resolution for {}", resolution.did);

                // Store the DID resolution information
                if let Some(peer_id) = source {
                    // Parse addresses and update phonebook
                    let addresses: Vec<libp2p::Multiaddr> = resolution.addresses
                        .iter()
                        .filter_map(|addr_str| addr_str.parse().ok())
                        .collect();

                    self.phonebook.add_peer(peer_id, addresses)?;

                    // TODO: Store the public key and DID document for future verification
                    debug!("Stored DID resolution data for {}", resolution.did);
                }
            }
            AstrumMessage::TrustUpdate(trust_update) => {
                info!("Received trust update from {} to {}", trust_update.from_did, trust_update.to_did);
                // TODO: Handle trust update logic
            }
        }
        Ok(())
    }

    /// Handle Kademlia events
    async fn handle_kademlia_event(&mut self, event: libp2p::kad::KademliaEvent) -> Result<()> {
        use libp2p::kad::KademliaEvent;

        match event {
            KademliaEvent::OutboundQueryProgressed { result, .. } => {
                debug!("Kademlia query progressed: {:?}", result);
            }
            KademliaEvent::RoutingUpdated { peer, .. } => {
                debug!("Kademlia routing updated for peer: {}", peer);
            }
            _ => {}
        }
        Ok(())
    }

    /// Handle mDNS events
    async fn handle_mdns_event(&mut self, event: libp2p::mdns::Event) -> Result<()> {
        use libp2p::mdns::Event;

        match event {
            Event::Discovered(list) => {
                for (peer_id, multiaddr) in list {
                    info!("Discovered peer {} at {}", peer_id, multiaddr);
                    self.phonebook.add_peer(peer_id, vec![multiaddr])?;
                }
            }
            Event::Expired(list) => {
                for (peer_id, _) in list {
                    debug!("mDNS entry expired for peer {}", peer_id);
                }
            }
        }
        Ok(())
    }

    /// Handle Identify events
    async fn handle_identify_event(&mut self, event: libp2p::identify::Event) -> Result<()> {
        use libp2p::identify::Event;

        match event {
            Event::Received { peer_id, info } => {
                info!("Received identify info from {}: protocol version {}", peer_id, info.protocol_version);
                self.phonebook.update_peer_addresses(&peer_id, info.listen_addrs)?;
            }
            Event::Sent { peer_id } => {
                debug!("Sent identify info to {}", peer_id);
            }
            Event::Pushed { peer_id } => {
                debug!("Pushed identify info to {}", peer_id);
            }
            Event::Error { peer_id, error } => {
                warn!("Identify error with peer {}: {}", peer_id, error);
            }
        }
        Ok(())
    }

    /// Handle Relay events
    async fn handle_relay_event(&mut self, event: libp2p::relay::client::Event) -> Result<()> {
        use libp2p::relay::client::Event;

        match event {
            Event::ReservationReqAccepted { relay_peer_id, .. } => {
                info!("Relay reservation accepted by {}", relay_peer_id);
            }
            Event::ReservationReqFailed { relay_peer_id, .. } => {
                warn!("Relay reservation failed with {}", relay_peer_id);
            }
            Event::OutboundCircuitEstablished { relay_peer_id, .. } => {
                info!("Outbound circuit established through {}", relay_peer_id);
            }
            Event::InboundCircuitEstablished { src_peer_id, .. } => {
                info!("Inbound circuit established from {}", src_peer_id);
            }
            _ => {}
        }
        Ok(())
    }

    /// Handle DCUtR events
    async fn handle_dcutr_event(&mut self, event: libp2p::dcutr::Event) -> Result<()> {
        use libp2p::dcutr::Event;

        match event {
            Event::InitiatedDirectConnectionUpgrade { remote_peer_id, .. } => {
                info!("Initiated direct connection upgrade with {}", remote_peer_id);
            }
            Event::RemoteInitiatedDirectConnectionUpgrade { remote_peer_id, .. } => {
                info!("Remote peer {} initiated direct connection upgrade", remote_peer_id);
            }
            Event::DirectConnectionUpgradeSucceeded { remote_peer_id } => {
                info!("Direct connection upgrade succeeded with {}", remote_peer_id);
            }
            Event::DirectConnectionUpgradeFailed { remote_peer_id, error } => {
                warn!("Direct connection upgrade failed with {}: {}", remote_peer_id, error);
            }
        }
        Ok(())
    }

    /// Periodic cleanup tasks
    async fn periodic_cleanup(&mut self) -> Result<()> {
        debug!("Running periodic cleanup");

        // Clean up old phonebook entries (older than 24 hours)
        self.phonebook.cleanup_old_entries(24 * 60 * 60)?;

        // Clean up inactive constellations (older than 7 days)
        self.constellation.cleanup_inactive_constellations(7 * 24 * 60 * 60)?;

        Ok(())
    }

    /// Announce presence to the network
    async fn announce_presence(&mut self) -> Result<()> {
        debug!("Announcing presence");

        // Create beacon announcement
        let announcement = crate::protocols::messages::BeaconAnnouncement {
            did: self.local_did.clone(),
            addresses: self.swarm.listeners().map(|addr| addr.to_string()).collect(),
            capabilities: vec!["gossipsub".to_string(), "kademlia".to_string()],
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };

        let message = crate::protocols::messages::AstrumMessage::BeaconAnnouncement(announcement);
        let data = serde_json::to_vec(&message)?;

        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| AstrumError::NetworkError(e.to_string()))?;

        Ok(())
    }

    /// Create a new constellation
    pub fn create_constellation(&mut self, name: Option<String>, is_private: bool) -> Result<String> {
        let constellation_id = self.constellation.create_constellation(
            name.clone(),
            is_private,
            self.config.constellation.trust_threshold,
        )?;

        info!("Created constellation: {}", constellation_id);

        // Announce constellation creation if not private
        if !is_private {
            self.announce_constellation_creation(&constellation_id, name)?;
        }

        Ok(constellation_id)
    }

    /// Join an existing constellation
    pub fn join_constellation(&mut self, constellation_id: &str) -> Result<()> {
        self.constellation.join_constellation(
            constellation_id,
            self.local_peer_id,
            crate::constellation::MemberRole::Member,
        )?;

        info!("Joined constellation: {}", constellation_id);

        // Announce constellation join
        self.announce_constellation_join(constellation_id)?;

        Ok(())
    }

    /// Leave a constellation
    pub fn leave_constellation(&mut self, constellation_id: &str) -> Result<()> {
        self.constellation.leave_constellation(constellation_id, self.local_peer_id)?;

        // Announce constellation leave
        self.announce_constellation_leave(constellation_id)?;

        info!("Left constellation: {}", constellation_id);
        Ok(())
    }

    /// Get connected peers count
    pub fn connected_peers_count(&self) -> usize {
        self.phonebook.get_connected_peers().len()
    }

    /// Get constellation count
    pub fn constellation_count(&self) -> usize {
        self.constellation.get_all_constellations().len()
    }

    /// Announce constellation creation
    fn announce_constellation_creation(&mut self, constellation_id: &str, _name: Option<String>) -> Result<()> {
        let announcement = crate::protocols::messages::AstrumMessage::ConstellationJoin(
            crate::protocols::messages::ConstellationJoin {
                constellation_id: constellation_id.to_string(),
                joiner_did: self.local_did.clone(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&announcement)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        Ok(())
    }

    /// Announce constellation join
    fn announce_constellation_join(&mut self, constellation_id: &str) -> Result<()> {
        let announcement = crate::protocols::messages::AstrumMessage::ConstellationJoin(
            crate::protocols::messages::ConstellationJoin {
                constellation_id: constellation_id.to_string(),
                joiner_did: self.local_did.clone(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&announcement)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        Ok(())
    }

    /// Announce constellation leave
    fn announce_constellation_leave(&mut self, constellation_id: &str) -> Result<()> {
        let announcement = crate::protocols::messages::AstrumMessage::ConstellationLeave(
            crate::protocols::messages::ConstellationLeave {
                constellation_id: constellation_id.to_string(),
                leaver_did: self.local_did.clone(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&announcement)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        Ok(())
    }

    /// Invite a peer to join a constellation
    pub fn invite_to_constellation(&mut self, constellation_id: &str, peer_id: libp2p::PeerId, message: Option<String>) -> Result<()> {
        let invite = crate::protocols::messages::AstrumMessage::ConstellationInvite(
            crate::protocols::messages::ConstellationInvite {
                constellation_id: constellation_id.to_string(),
                inviter_did: self.local_did.clone(),
                invited_peer: peer_id,
                message,
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&invite)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        info!("Sent constellation invite to {} for constellation {}", peer_id, constellation_id);
        Ok(())
    }

    /// Update trust score for a peer in a constellation
    pub fn update_peer_trust(&mut self, peer_id: &libp2p::PeerId, constellation_id: Option<&str>, trust_score: f64) -> Result<()> {
        // Update local trust score
        self.phonebook.update_trust_score(peer_id, trust_score)?;

        if let Some(constellation_id) = constellation_id {
            self.constellation.update_member_trust_score(constellation_id, peer_id, trust_score)?;
        }

        // Announce trust update
        let trust_update = crate::protocols::messages::AstrumMessage::TrustUpdate(
            crate::protocols::messages::TrustUpdate {
                from_did: self.local_did.clone(),
                to_did: crate::did::AstrumDid::new(*peer_id),
                trust_score,
                constellation_id: constellation_id.map(|s| s.to_string()),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&trust_update)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::BEACON_DISCOVERY_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        info!("Updated trust score for {} to {}", peer_id, trust_score);
        Ok(())
    }

    /// Publish DID document to the network
    pub fn publish_did_document(&mut self) -> Result<()> {
        let public_key = self.keypair.public();
        let addresses: Vec<libp2p::Multiaddr> = self.swarm.listeners().cloned().collect();

        let _did_document = self.local_did.create_did_document(&public_key, addresses);

        let resolution_message = crate::protocols::messages::AstrumMessage::DidResolution(
            crate::protocols::messages::DidResolution {
                did: self.local_did.clone(),
                addresses: self.swarm.listeners().map(|addr| addr.to_string()).collect(),
                public_key: public_key.encode_protobuf(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            }
        );

        let data = serde_json::to_vec(&resolution_message)?;
        let topic = libp2p::gossipsub::IdentTopic::new(constants::DID_RESOLUTION_TOPIC);
        self.swarm.behaviour_mut().publish_message(topic, data)
            .map_err(|e| crate::error::AstrumError::NetworkError(e.to_string()))?;

        info!("Published DID document for {}", self.local_did);
        Ok(())
    }

    /// Request DID resolution for a peer
    pub fn resolve_did(&mut self, did: &crate::did::AstrumDid) -> Result<()> {
        // Subscribe to DID resolution topic if not already subscribed
        let topic = libp2p::gossipsub::IdentTopic::new(constants::DID_RESOLUTION_TOPIC);
        let _ = self.swarm.behaviour_mut().subscribe_to_topic(&topic);

        // For now, we just log the request. In a full implementation,
        // we would query the DHT or send a specific resolution request
        info!("Requesting DID resolution for {}", did);

        // TODO: Implement actual DID resolution query mechanism
        // This could involve:
        // 1. Querying the Kademlia DHT for the DID
        // 2. Sending a direct resolution request to known peers
        // 3. Broadcasting a resolution request on gossipsub

        Ok(())
    }

    /// Get the DID document for the local beacon
    pub fn get_local_did_document(&self) -> crate::did::DidDocument {
        let public_key = self.keypair.public();
        let addresses: Vec<libp2p::Multiaddr> = self.swarm.listeners().cloned().collect();
        self.local_did.create_did_document(&public_key, addresses)
    }

    /// Verify a message signature using a DID
    pub fn verify_did_signature(&self, did: &crate::did::AstrumDid, message: &[u8], signature: &[u8]) -> Result<bool> {
        // In a real implementation, we would resolve the DID to get the public key
        // For now, we'll use the peer's public key if we have it
        if did.peer_id() == &self.local_peer_id {
            let public_key = self.keypair.public();
            did.verify_signature(message, signature, &public_key)
        } else {
            // TODO: Resolve DID to get public key
            Err(crate::error::AstrumError::DidError("Cannot verify signature for remote DID without resolution".to_string()))
        }
    }
}
