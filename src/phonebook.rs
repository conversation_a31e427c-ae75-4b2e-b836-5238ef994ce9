use libp2p::{PeerId, Multiaddr};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::{SystemTime, UNIX_EPOCH};
use crate::error::Result;
use crate::did::AstrumDid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PeerInfo {
    pub did: AstrumDid,
    pub addresses: Vec<Multiaddr>,
    pub last_seen: u64,
    pub connection_status: ConnectionStatus,
    pub trust_score: f64,
    pub constellation_memberships: Vec<String>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionStatus {
    Connected,
    Disconnected,
    Connecting,
    Failed,
    Unknown,
}

#[derive(Debug)]
pub struct Phonebook {
    peers: HashMap<PeerId, PeerInfo>,
    file_path: Option<PathBuf>,
    max_entries: usize,
}

impl Phonebook {
    pub fn new(file_path: Option<PathBuf>, max_entries: usize) -> Self {
        Self {
            peers: HashMap::new(),
            file_path,
            max_entries,
        }
    }

    pub fn add_peer(&mut self, peer_id: PeerId, addresses: Vec<Multiaddr>) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let peer_info = PeerInfo {
            did: AstrumDid::new(peer_id),
            addresses,
            last_seen: now,
            connection_status: ConnectionStatus::Unknown,
            trust_score: 0.5, // Default neutral trust
            constellation_memberships: Vec::new(),
            metadata: HashMap::new(),
        };

        // If we're at capacity, remove the oldest entry
        if self.peers.len() >= self.max_entries {
            self.remove_oldest_peer();
        }

        self.peers.insert(peer_id, peer_info);
        self.persist_if_configured()?;
        Ok(())
    }

    pub fn update_peer_status(&mut self, peer_id: &PeerId, status: ConnectionStatus) -> Result<()> {
        if let Some(peer_info) = self.peers.get_mut(peer_id) {
            peer_info.connection_status = status;
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();
            peer_info.last_seen = now;
            self.persist_if_configured()?;
        }
        Ok(())
    }

    pub fn update_peer_addresses(&mut self, peer_id: &PeerId, addresses: Vec<Multiaddr>) -> Result<()> {
        if let Some(peer_info) = self.peers.get_mut(peer_id) {
            peer_info.addresses = addresses;
            self.persist_if_configured()?;
        }
        Ok(())
    }

    pub fn update_trust_score(&mut self, peer_id: &PeerId, score: f64) -> Result<()> {
        if let Some(peer_info) = self.peers.get_mut(peer_id) {
            peer_info.trust_score = score.clamp(0.0, 1.0);
            self.persist_if_configured()?;
        }
        Ok(())
    }

    pub fn get_peer(&self, peer_id: &PeerId) -> Option<&PeerInfo> {
        self.peers.get(peer_id)
    }

    pub fn get_all_peers(&self) -> &HashMap<PeerId, PeerInfo> {
        &self.peers
    }

    pub fn get_connected_peers(&self) -> Vec<&PeerInfo> {
        self.peers
            .values()
            .filter(|info| info.connection_status == ConnectionStatus::Connected)
            .collect()
    }

    pub fn get_trusted_peers(&self, min_trust: f64) -> Vec<&PeerInfo> {
        self.peers
            .values()
            .filter(|info| info.trust_score >= min_trust)
            .collect()
    }

    pub fn remove_peer(&mut self, peer_id: &PeerId) -> Result<()> {
        self.peers.remove(peer_id);
        self.persist_if_configured()?;
        Ok(())
    }

    fn remove_oldest_peer(&mut self) {
        if let Some((oldest_peer_id, _)) = self.peers
            .iter()
            .min_by_key(|(_, info)| info.last_seen)
            .map(|(id, info)| (*id, info.clone()))
        {
            self.peers.remove(&oldest_peer_id);
        }
    }

    pub fn load_from_disk(&mut self) -> Result<()> {
        if let Some(path) = &self.file_path {
            if path.exists() {
                let content = std::fs::read_to_string(path)?;
                let peers: HashMap<PeerId, PeerInfo> = serde_json::from_str(&content)?;
                self.peers = peers;
            }
        }
        Ok(())
    }

    pub fn persist_to_disk(&self) -> Result<()> {
        if let Some(path) = &self.file_path {
            if let Some(parent) = path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            let content = serde_json::to_string_pretty(&self.peers)?;
            std::fs::write(path, content)?;
        }
        Ok(())
    }

    fn persist_if_configured(&self) -> Result<()> {
        if self.file_path.is_some() {
            self.persist_to_disk()?;
        }
        Ok(())
    }

    pub fn cleanup_old_entries(&mut self, max_age_seconds: u64) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let old_peers: Vec<PeerId> = self.peers
            .iter()
            .filter(|(_, info)| now - info.last_seen > max_age_seconds)
            .map(|(id, _)| *id)
            .collect();

        for peer_id in old_peers {
            self.peers.remove(&peer_id);
        }

        self.persist_if_configured()?;
        Ok(())
    }

    pub fn size(&self) -> usize {
        self.peers.len()
    }
}
