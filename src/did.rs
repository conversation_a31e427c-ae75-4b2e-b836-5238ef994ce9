use libp2p::PeerId;
use serde::{Deserialize, Serialize};
use std::fmt;
use crate::error::{AstrumError, Result};

/// Astrum Decentralized Identifier (DID) based on libp2p PeerId
#[derive(<PERSON>bug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct AstrumDid {
    peer_id: PeerId,
    method: String,
    network: Option<String>,
}

impl AstrumDid {
    const METHOD: &'static str = "astrum";
    
    pub fn new(peer_id: PeerId) -> Self {
        Self {
            peer_id,
            method: Self::METHOD.to_string(),
            network: None,
        }
    }
    
    pub fn with_network(peer_id: PeerId, network: String) -> Self {
        Self {
            peer_id,
            method: Self::METHOD.to_string(),
            network: Some(network),
        }
    }
    
    pub fn peer_id(&self) -> &PeerId {
        &self.peer_id
    }
    
    pub fn method(&self) -> &str {
        &self.method
    }
    
    pub fn network(&self) -> Option<&str> {
        self.network.as_deref()
    }
    
    /// Parse a DID string into an AstrumDid
    pub fn parse(did_string: &str) -> Result<Self> {
        if !did_string.starts_with("did:astrum:") {
            return Err(AstrumError::DidError(
                "Invalid DID format: must start with 'did:astrum:'".to_string()
            ));
        }
        
        let parts: Vec<&str> = did_string.split(':').collect();
        if parts.len() < 3 {
            return Err(AstrumError::DidError(
                "Invalid DID format: insufficient parts".to_string()
            ));
        }
        
        let peer_id_str = parts[2];
        let peer_id = peer_id_str.parse::<PeerId>()
            .map_err(|e| AstrumError::DidError(format!("Invalid PeerId: {}", e)))?;
        
        let network = if parts.len() > 3 {
            Some(parts[3].to_string())
        } else {
            None
        };
        
        Ok(Self {
            peer_id,
            method: Self::METHOD.to_string(),
            network,
        })
    }
    
    /// Convert to DID string representation
    pub fn to_string(&self) -> String {
        match &self.network {
            Some(network) => format!("did:{}:{}:{}", self.method, self.peer_id, network),
            None => format!("did:{}:{}", self.method, self.peer_id),
        }
    }

    /// Create a DID document for this DID
    pub fn create_did_document(&self, public_key: &libp2p::identity::PublicKey, addresses: Vec<libp2p::Multiaddr>) -> DidDocument {
        DidDocument {
            context: vec!["https://www.w3.org/ns/did/v1".to_string()],
            id: self.to_string(),
            verification_method: vec![VerificationMethod {
                id: format!("{}#key-1", self.to_string()),
                type_: "Ed25519VerificationKey2020".to_string(),
                controller: self.to_string(),
                public_key_multibase: format!("z{}", bs58::encode(public_key.encode_protobuf()).into_string()),
            }],
            authentication: vec![format!("{}#key-1", self.to_string())],
            service: addresses.into_iter().enumerate().map(|(i, addr)| {
                Service {
                    id: format!("{}#service-{}", self.to_string(), i),
                    type_: "AstrumNetwork".to_string(),
                    service_endpoint: addr.to_string(),
                }
            }).collect(),
        }
    }

    /// Resolve a DID to get its document
    pub async fn resolve(&self) -> Result<DidDocument> {
        // In a real implementation, this would query the network
        // For now, return an error indicating resolution is not implemented
        Err(AstrumError::DidError("DID resolution not yet implemented".to_string()))
    }

    /// Verify a signature using this DID
    pub fn verify_signature(&self, message: &[u8], signature: &[u8], public_key: &libp2p::identity::PublicKey) -> Result<bool> {
        // For now, we'll use a simplified verification approach
        // In a real implementation, this would use the proper cryptographic verification
        if let Ok(ed25519_key) = public_key.clone().try_into_ed25519() {
            Ok(ed25519_key.verify(message, signature))
        } else {
            Err(AstrumError::DidError("Unsupported key type for signature verification".to_string()))
        }
    }
}

impl fmt::Display for AstrumDid {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.to_string())
    }
}

impl From<PeerId> for AstrumDid {
    fn from(peer_id: PeerId) -> Self {
        Self::new(peer_id)
    }
}

/// DID Document structure following W3C DID specification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DidDocument {
    #[serde(rename = "@context")]
    pub context: Vec<String>,
    pub id: String,
    #[serde(rename = "verificationMethod")]
    pub verification_method: Vec<VerificationMethod>,
    pub authentication: Vec<String>,
    pub service: Vec<Service>,
}

/// Verification method for DID documents
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationMethod {
    pub id: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub controller: String,
    #[serde(rename = "publicKeyMultibase")]
    pub public_key_multibase: String,
}

/// Service endpoint for DID documents
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Service {
    pub id: String,
    #[serde(rename = "type")]
    pub type_: String,
    #[serde(rename = "serviceEndpoint")]
    pub service_endpoint: String,
}

/// DID Resolution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DidResolutionResult {
    #[serde(rename = "didDocument")]
    pub did_document: Option<DidDocument>,
    #[serde(rename = "didResolutionMetadata")]
    pub did_resolution_metadata: DidResolutionMetadata,
    #[serde(rename = "didDocumentMetadata")]
    pub did_document_metadata: DidDocumentMetadata,
}

/// DID Resolution metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DidResolutionMetadata {
    #[serde(rename = "contentType")]
    pub content_type: Option<String>,
    pub error: Option<String>,
}

/// DID Document metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DidDocumentMetadata {
    pub created: Option<String>,
    pub updated: Option<String>,
    pub deactivated: Option<bool>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use libp2p::identity;
    
    #[test]
    fn test_astrum_did_creation() {
        let keypair = identity::Keypair::generate_ed25519();
        let peer_id = PeerId::from(keypair.public());
        let did = AstrumDid::new(peer_id);
        
        assert_eq!(did.peer_id(), &peer_id);
        assert_eq!(did.method(), "astrum");
        assert_eq!(did.network(), None);
    }
    
    #[test]
    fn test_astrum_did_with_network() {
        let keypair = identity::Keypair::generate_ed25519();
        let peer_id = PeerId::from(keypair.public());
        let network = "testnet".to_string();
        let did = AstrumDid::with_network(peer_id, network.clone());
        
        assert_eq!(did.network(), Some("testnet"));
    }
    
    #[test]
    fn test_did_string_conversion() {
        let keypair = identity::Keypair::generate_ed25519();
        let peer_id = PeerId::from(keypair.public());
        let did = AstrumDid::new(peer_id);
        
        let did_string = did.to_string();
        assert!(did_string.starts_with("did:astrum:"));
        
        let parsed_did = AstrumDid::parse(&did_string).unwrap();
        assert_eq!(did, parsed_did);
    }
}
