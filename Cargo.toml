[package]
name = "astrum"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "astrum_beacon"
path = "src/main.rs"

[dependencies]
libp2p = { version = "0.51.1", features = [
    "kad",
    "tokio",
    "gossipsub",
    "mdns",
    "noise",
    "tcp",
    "yamux",
    "relay",
    "identify",
    "dcutr",
    "ping",
    "macros",
    "serde"
] }
tokio = { version = "1.28.2", features = ["full"] }
futures = "0.3.28"
env_logger = "0.10.0"
clap = { version = "4.0", features = ["derive"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
bs58 = "0.5"
